{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist && esbuild server/migrate.ts --platform=node --packages=external --bundle --format=esm --outfile=dist/migrate.js", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:migrate": "tsx server/migrate.ts", "db:generate": "drizzle-kit generate:pg", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@fal-ai/client": "^1.4.0", "@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@react-three/drei": "^9.102.6", "@react-three/fiber": "^8.15.19", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@tabler/icons-react": "^3.1.0", "@tanstack/react-query": "^5.28.9", "@tanstack/react-table": "^8.15.3", "@types/howler": "^2.2.12", "@types/three": "^0.174.0", "@types/uuid": "^10.0.0", "axios": "^1.7.2", "cannon-es": "^0.20.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "ethers": "^5.7.2", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.18.2", "groq-sdk": "^0.19.0", "howler": "^2.2.4", "input-otp": "^1.2.4", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "nipplejs": "^0.10.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "recharts": "^2.13.0", "sharp": "^0.34.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "three": "^0.174.0", "uuid": "^11.1.0", "vaul": "^1.1.0", "viem": "^2.30.1", "wagmi": "^2.15.4", "wouter": "^3.6.0", "ws": "^8.18.1", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@replit/vite-plugin-cartographer": "^0.0.4", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/testing-library__jest-dom": "^6.0.0", "@types/ws": "^8.18.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "babel-jest": "^29.7.0", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "ts-jest": "^29.1.2", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}