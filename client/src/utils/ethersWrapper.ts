// Ethers v5 wrapper to provide consistent API
// This wrapper handles the differences between ethers v5 import structure and usage

// Import ethers v5 components using namespace import
import * as ethersLib from 'ethers';

// Create a wrapper that matches the expected structure
export const ethers = {
  providers: ethersLib.providers,
  utils: ethersLib.utils,
  Contract: ethersLib.Contract,
  Wallet: ethersLib.Wallet,
  BigNumber: ethersLib.BigNumber,
  constants: ethersLib.constants,
  // Add other commonly used ethers components as needed
};

// Export individual components for direct use
export const { providers, utils, Contract, Wallet, BigNumber, constants } = ethers;

// Default export for compatibility
export default ethers;
