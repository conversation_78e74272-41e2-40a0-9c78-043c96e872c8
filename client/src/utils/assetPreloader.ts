/**
 * Utility for preloading critical game assets
 */

import * as THREE from 'three';

// Singleton texture loader
const textureLoader = new THREE.TextureLoader();

// Cache for loaded textures
const textureCache = new Map<string, THREE.Texture>();

// List of critical textures to preload (only the most essential ones)
const criticalTextures = [
  // Only the first level texture set for immediate game start
  '/assets/textures/level/floor1.svg',
  '/assets/textures/level/wall1.svg',
  '/assets/textures/level/ceiling1.svg',
  // Essential weapon effects
  '/assets/textures/phase_effect.png',
  // Essential enemy texture
  '/assets/textures/specter_sprite.png',
];

// Secondary textures to load after critical ones
const secondaryTextures = [
  '/assets/textures/gravity_effect.png',
  '/assets/textures/time_effect.png',
  '/assets/textures/orange.svg',
  // Additional level textures
  '/assets/textures/level/floor2.svg',
  '/assets/textures/level/floor3.svg',
  '/assets/textures/level/floor4.svg',
  '/assets/textures/level/floor5.svg',
  '/assets/textures/level/wall2.svg',
  '/assets/textures/level/wall3.svg',
  '/assets/textures/level/wall4.svg',
  '/assets/textures/level/wall5.svg',
  '/assets/textures/level/ceiling2.svg',
  '/assets/textures/level/ceiling3.svg',
  '/assets/textures/level/ceiling4.svg',
  '/assets/textures/level/ceiling5.svg',
];

/**
 * Preloads critical game assets in the background
 * This helps reduce visible loading when transitioning to the game
 */
export const preloadCriticalAssets = (): Promise<void> => {
  return new Promise((resolve) => {
    let loadedCount = 0;
    const totalAssets = criticalTextures.length;

    // Function to check if all assets are loaded
    const checkAllLoaded = () => {
      loadedCount++;
      if (loadedCount >= totalAssets) {
        console.log('Critical assets preloaded, starting secondary loading');
        resolve();
        // Start loading secondary assets in background after critical ones are done
        preloadSecondaryAssets();
      }
    };

    // Preload critical textures first
    criticalTextures.forEach(texturePath => {
      textureLoader.load(
        texturePath,
        (texture) => {
          console.log(`Preloaded critical texture: ${texturePath}`);
          textureCache.set(texturePath, texture);
          checkAllLoaded();
        },
        undefined, // onProgress is not supported by TextureLoader
        (error) => {
          console.warn(`Failed to preload critical texture: ${texturePath}`, error);
          checkAllLoaded(); // Still count as "loaded" to avoid blocking
        }
      );
    });

    // If no assets to load, resolve immediately
    if (totalAssets === 0) {
      resolve();
    }
  });
};

/**
 * Preloads secondary assets in the background (non-blocking)
 */
const preloadSecondaryAssets = (): void => {
  console.log('Starting secondary asset preloading');

  secondaryTextures.forEach(texturePath => {
    textureLoader.load(
      texturePath,
      (texture) => {
        console.log(`Preloaded secondary texture: ${texturePath}`);
        textureCache.set(texturePath, texture);
      },
      undefined,
      (error) => {
        console.warn(`Failed to preload secondary texture: ${texturePath}`, error);
      }
    );
  });
};

/**
 * Get a cached texture or load it if not cached
 */
export const getCachedTexture = (texturePath: string): THREE.Texture | null => {
  return textureCache.get(texturePath) || null;
};

/**
 * Load a texture with caching
 */
export const loadTextureWithCache = (texturePath: string): Promise<THREE.Texture> => {
  return new Promise((resolve, reject) => {
    // Check cache first
    const cached = textureCache.get(texturePath);
    if (cached) {
      resolve(cached);
      return;
    }

    // Load and cache
    textureLoader.load(
      texturePath,
      (texture) => {
        textureCache.set(texturePath, texture);
        resolve(texture);
      },
      undefined,
      (error) => {
        reject(error);
      }
    );
  });
};

// Export the loader for direct access if needed
export { textureLoader };
