import * as THREE from "three";
import * as <PERSON><PERSON><PERSON><PERSON> from "cannon-es";
import { PointerLockControls } from "three/examples/jsm/controls/PointerLockControls";
import { EffectComposer } from "three/examples/jsm/postprocessing/EffectComposer";
import { RenderPass } from "three/examples/jsm/postprocessing/RenderPass";
import { UnrealBloomPass } from "three/examples/jsm/postprocessing/UnrealBloomPass";
import Player from "../entities/Player.js";
import InputHandler from "./InputHandler.js";
import LevelGenerator from "../world/LevelGenerator.js";
import { DungeonManager } from "../world/DungeonManager.js";
import { DungeonDifficulty } from "../world/DungeonGenerator.js";
import Specter from "../entities/Specter.js";
import { PetSpecter } from "../entities/PetSpecter.js";
import OrangeEnemy from "../entities/OrangeEnemy.js";
import ShattershiftRifle from "../weapons/ShattershiftRifle.js";
import Pickup, { PickupType } from "../entities/Pickup.js";
import { AmmoType, EffectType, SpecterType } from "../types.js";
import { audioManager } from "../audio/AudioManager.js";
import { MerchGenieDialogWithNFT } from "../ui/MerchGenieDialogWithNFT.js";
import NFTMintDialog from "@/components/NFTMintDialog.jsx";
import NFTBasedPetDialog from "@/components/NFTBasedPetDialog.jsx";
import AIPetGenerationDialog from "@/components/AIPetGenerationDialog.jsx";
import { PetService, ApiPetSpecter } from "@/services/petService.js";
import {
  AMMO_TYPES,
  GRAVITY,
  WORLD_SIZE,
  MAX_AMMO,
  PLAYER_MAX_HEALTH,
  PLAYER_JETPACK_MAX_FUEL,
  PLAYER_MOVE_SPEED,
} from "../constants.js";
import { v4 as uuidv4 } from "uuid";
import { WeaponEffectData } from "@shared/schema.js";
import MobileControls from "../controls/MobileControls.jsx";
import { ColiseumArena } from "../world/ColiseumArena.js";

// Add these custom event type declarations at the top of the file, before any imports

// Define custom event types
interface PetRecallEvent extends CustomEvent {
  detail: {
    petId: string;
  };
}

interface PetDeployEvent extends CustomEvent {
  detail: {
    petId: string;
  };
}

class GameEngine {
  // Three.js components
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private controls: PointerLockControls;
  private composer: EffectComposer;
  private isMobile: boolean = false;
  private mobileControls: React.ReactElement | null = null;

  // Physics world
  private world: CANNON.World;
  private physicsMaterials: { [key: string]: CANNON.Material };

  // Game entities
  public player: Player;
  private rifle: ShattershiftRifle;
  private level: LevelGenerator;
  public dungeonManager: DungeonManager | null = null;
  private specters: Specter[] = [];
  private petSpecters: PetSpecter[] = [];
  private input: InputHandler;

  // Homing missile optimization
  private nearestSpecter: Specter | null = null;
  private lastHomingUpdateTime: number = 0;
  private homingUpdateInterval: number = 0.2; // Update target every 200ms instead of every frame
  private merchGenieDialog: MerchGenieDialogWithNFT | null = null;
  private nftMintDialog: any = null;
  private nftBasedPetDialog: any = null;
  private aiPetGenerationDialog: any = null;

  // Portal system has been removed

  // Environment
  private skybox: THREE.Mesh | null = null;
  private starsGroup: THREE.Group | null = null;

  // Game state
  private running: boolean = false;
  private clock: THREE.Clock;
  private elapsedTime: number = 0;
  private totalEnemiesSpawned: number = 0;
  private currentLevelEnemies: number = 10; // Starting with 10 for first level
  private currentAmmoType: AmmoType = AmmoType.GravityWell;
  private ammo: number[] = [MAX_AMMO, MAX_AMMO, MAX_AMMO]; // Initial ammo counts for each type
  private orangeEnemySpawned: boolean = false; // Track if the orange enemy has been spawned
  private hasOrangePet: boolean = false; // Track if the player has the OrangePet
  private petSpectersLoaded: boolean = false; // Track if pet specters have been loaded

  // Flag to track if this is a newly created game engine
  public isNewlyCreated: boolean = true;

  // Multiplayer - other players
  private otherPlayers: Map<string, {
    id: string,
    model: THREE.Group,
    position: THREE.Vector3,
    rotation: THREE.Vector3,
    health: number,
    jetpackFuel: number,
    lastUpdate: number,
    name: string,
    team?: number
  }> = new Map();

  // Effects and projectiles
  private activeEffects: {
    effect: THREE.Object3D;
    type: EffectType;
    position: THREE.Vector3;
    radius: number;
    timeLeft: number;
    body?: CANNON.Body;
    id?: string; // Add ID for network synchronization
    createdAt?: number; // Timestamp when effect was created
  }[] = [];

  // Particle system limits
  private readonly MAX_ACTIVE_EFFECTS = 10; // Maximum number of active weapon effects
  private readonly MAX_PARTICLES_PER_EFFECT = 50; // Maximum particles per effect
  private readonly MAX_TOTAL_PARTICLES = 300; // Maximum total particles across all effects

  // Pickups collection
  private pickups: Pickup[] = [];

  // PVP Tournament Battle Client
  private tournamentBattleClient: any = null;
  private pvpArenaManager: ColiseumArena | null = null; // Changed type to ColiseumArena
  private currentBattleId: string | null = null;
  private isPvpArenaInitialized: boolean = false;
  private isPvpArenaReady: boolean = false; // Flag to indicate arena + spectator setup completion
  private spectatorPositionSet: boolean = false; // Flag to ensure spectator is positioned only once

  // Event callbacks
  public onSpecterCapture: (specter: SpecterType) => void = () => {};
  public onAmmoChange: (ammo: number[]) => void = () => {};
  public onAmmoTypeChange: (type: string) => void = () => {};
  public onGameOver: () => void = () => {};
  public onHealthChange: (health: number) => void = () => {};
  public onJetpackFuelChange: (fuel: number) => void = () => {};
  public onPetManagementOpen: (pets: PetSpecter[]) => void = () => {};

  // Multiplayer effect callbacks
  public onWeaponEffectCreated: (effectData: WeaponEffectData) => void = () => {};
  public onWeaponEffectRemoved: (effectId: string) => void = () => {};

  // Multiplayer player position callback
  public onPlayerPositionUpdate: (position: THREE.Vector3, rotation: THREE.Vector3) => void = () => {};

  // Network mode flag
  private isNetworkMode: boolean = false;
  private isSpectatorMode: boolean = false;
  private shouldLoadPetSpecters: boolean = true;
  private serverSpecterIds: Map<number, number> = new Map(); // Map local specter ids to server ids

  // Interaction tracking
  private currentInteractionTarget: THREE.Object3D | null = null; // Store the object the player is looking at for interaction
  private isShowingInteractionPrompt: boolean = false;
  private interactionPromptElement: HTMLElement | null = null;

  // Level system
  private currentLevel: number = 1;  // Start at level 1
  public onLevelChange: (level: number) => void = () => {};

  // Dungeon system
  public onDungeonEnter: () => void = () => {};
  public onDungeonExit: () => void = () => {};
  public onDungeonComplete: (rewards: any) => void = () => {};
  public onDungeonFailed: () => void = () => {};
  public onDungeonEnemyDefeated: (enemyData: any) => void = () => {};
  public onDungeonBossDefeated: (bossData: any) => void = () => {};
  public onDungeonLootCollected: (lootData: any) => void = () => {};
  public onDungeonRoomCleared: (roomKey: string) => void = () => {};

  // Fix: Add missing property declarations for linter errors
  // Add these property declarations near the top of the GameEngine class
  private isPvpArena: boolean = false;
  private isPaused: boolean = false;
  private spectatorSpeed: number = 10.0; // Speed for spectator movement
  private spectatorControlsInitialized: boolean = false; // Flag to ensure controls are set up once

  // Store all user pet specters for UI access (not just active ones)
  private allUserPetSpecters: ApiPetSpecter[] = [];

  constructor(container: HTMLElement, isMobile: boolean = false, isSpectatorMode: boolean = false) {
    // Set mobile flag
    this.isMobile = isMobile;
    // Set spectator mode flag if provided
    this.isSpectatorMode = isSpectatorMode;

    // Start background music
    audioManager.playRandomMusic();

    // Create scene
    this.scene = new THREE.Scene();
    // We'll replace the simple background color with a skybox
    this.setupSkybox();
    // Set fog to match our gloomy gray-ish blue sky with low density
    this.scene.fog = new THREE.FogExp2(0x5a6b88, 0.0015);

    // Setup camera
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000,
    );
    this.camera.position.set(0, 2, 0);

    // Setup renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;

    // Setup EffectComposer for bloom
    this.composer = new EffectComposer(this.renderer);
    this.composer.addPass(new RenderPass(this.scene, this.camera));

    // Add UnrealBloomPass for SLIGHT bloom
    const bloomPass = new UnrealBloomPass(
      new THREE.Vector2(window.innerWidth, window.innerHeight),
      0.5, // strength - lower for slight bloom
      0.1, // radius - adjust for desired spread
      0.85 // threshold - higher means only brighter parts bloom
    );
    this.composer.addPass(bloomPass);

    try {
      // Try to append the renderer to the container
      container.appendChild(this.renderer.domElement);
      //console.log("Successfully appended renderer to container");
    } catch (error) {
      console.error("Error appending renderer to container:", error);
      throw new Error(
        "Failed to append renderer to container: " +
          (error instanceof Error ? error.message : String(error)),
      );
    }

    // Setup pointer lock controls (even on mobile, but we won't use them for input)
    this.controls = new PointerLockControls(
      this.camera,
      this.renderer.domElement,
    );
    this.controls.pointerSpeed = 0.8;

    // Handle pointer lock errors gracefully
    document.addEventListener('pointerlockerror', (event) => {
      console.warn('Pointer lock error encountered, attempting recovery');
      // Release the lock if it's already locked
      if (document.pointerLockElement) {
        document.exitPointerLock();
      }
    });

    // Setup physics world
    this.world = new CANNON.World({
      gravity: new CANNON.Vec3(0, GRAVITY, 0),
    });

    // Setup physics materials
    this.physicsMaterials = {
      player: new CANNON.Material("player"),
      ground: new CANNON.Material("ground"),
      specter: new CANNON.Material("specter"),
    };

    // Create contact material behavior
    const playerGroundContactMaterial = new CANNON.ContactMaterial(
      this.physicsMaterials.player,
      this.physicsMaterials.ground,
      {
        friction: 0.01, // CRITICAL FIX: Almost zero friction for smoother movement
        restitution: 0.01, // Significantly reduce restitution to prevent bouncing
        contactEquationStiffness: 1e7, // Decrease stiffness for softer contact
        contactEquationRelaxation: 5, // Increase relaxation further
      },
    );
    this.world.addContactMaterial(playerGroundContactMaterial);

    // Create level
    this.level = new LevelGenerator(this.scene, this.world);
    this.level.generateInitialLevel();

    // Create player
    this.player = new Player(
      this.scene,
      this.world,
      this.camera,
      this.controls,
      this.physicsMaterials.player,
    );

    // Create dungeon manager
    this.dungeonManager = new DungeonManager(this.scene, this.world, this.player);

    // Set up dungeon manager event handlers
    this.setupDungeonEventHandlers();

    // Initialize player's score to 0
    this.player.score = 0;

    // Set up player event handlers
    this.player.onHealthChange = (health) => {
      this.onHealthChange(health);

      // Check for game over condition
      if (health <= 0) {
        this.onGameOver();
      }
    };

    this.player.onJetpackFuelChange = (fuel) => {
      this.onJetpackFuelChange(fuel);
    };

    // Initial health and fuel UI updates
    this.onHealthChange(PLAYER_MAX_HEALTH);
    this.onJetpackFuelChange(PLAYER_JETPACK_MAX_FUEL);

    // Create rifle
    this.rifle = new ShattershiftRifle(this.scene, this.camera);

    // Set the player's weapon reference
    (this.player as any).weapon = this.rifle;

    // Create input handler with mobile flag and controls
    this.input = new InputHandler(container, this.controls, this.isMobile);

    // Set up event listener for pet specter purchase
    document.addEventListener('purchasePetSpecter', ((event: CustomEvent) => {
      const { type, name, cost, customImageUrl } = event.detail;

      // Create the pet specter in the game
      this.createPetSpecter(type, name, undefined, customImageUrl);

      // Save the pet specter to the database
      this.savePetSpecterToDatabase(type, name, customImageUrl);
    }) as EventListener);

    // Set up event listener for pet specter NFT minting
    document.addEventListener('mintPetSpecterNFT', ((event: CustomEvent) => {
      const { type, name } = event.detail;
      this.showNFTMintDialog(type, name);
    }) as EventListener);

    // Set up event listener for NFT-based pet generation
    document.addEventListener('generatePetFromNFT', (() => {
      this.showNFTBasedPetDialog();
    }) as EventListener);

    // Set up event listener for AI-based pet generation
    document.addEventListener('generateAIPet', (() => {
      this.showAIPetGenerationDialog();
    }) as EventListener);

    // Set up event listener for pet behavior change
    document.addEventListener('petBehaviorChange', ((event: CustomEvent) => {
      const { petId, newState } = event.detail;

      // Find the pet with this ID and update its behavior state
      for (const pet of this.petSpecters) {
        if (pet.id === petId) {
          pet.behaviorState = newState;
          //console.log(`Game Engine: Changed pet ${pet.name} behavior to ${newState}`);
          break;
        }
      }
    }) as EventListener);

    // Set up clock
    this.clock = new THREE.Clock();

    // Listen for window resize
    window.addEventListener('resize', this.onWindowResize.bind(this));

    // Set up key listeners
    window.addEventListener('keydown', this.onKeyDown.bind(this));

    // Show the interaction prompt when initialized
    this.interactionPromptElement = document.createElement('div');

    // Create MerchGenie dialog with NFT support
    this.merchGenieDialog = new MerchGenieDialogWithNFT(this.player);

    // Enhanced lighting system with cooler colors for a gloomier atmosphere
    // Add ambient light with reduced intensity for a gloomier atmosphere
    const ambientLight = new THREE.AmbientLight(0x7a8a99, 2.0);
    this.scene.add(ambientLight);

    // Add directional light (like a sun) with cooler color and reduced intensity
    const directionalLight = new THREE.DirectionalLight(0xc8d8e8, 1.8);
    directionalLight.position.set(50, 200, 100);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048; // Increased resolution for better shadows
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 500;
    this.scene.add(directionalLight);

    // Add secondary fill light from opposite direction with reduced intensity
    const fillLight = new THREE.DirectionalLight(0x8899aa, 1.2);
    fillLight.position.set(-50, 100, -100);
    this.scene.add(fillLight);

    // Add several point lights with cooler colors to illuminate the scene
    const pointLight1 = new THREE.PointLight(0x6677aa, 1.5, 200);
    pointLight1.position.set(0, 50, 0);
    this.scene.add(pointLight1);

    // Add a second point light for more global illumination
    const pointLight2 = new THREE.PointLight(0x8899bb, 1.0, 150);
    pointLight2.position.set(50, 30, 50);
    this.scene.add(pointLight2);

    // Add a third point light for more global illumination
    const pointLight3 = new THREE.PointLight(0x99aabb, 1.0, 150);
    pointLight3.position.set(-50, 30, -50);
    this.scene.add(pointLight3);

    // Create initial specters
    this.spawnSpecters();

    // Load player's pet specters asynchronously (non-blocking)
    this.loadPetSpectersAsync();

    //console.log("Game engine initialization complete");
    // Add level indicator creation - needs implementation
    this.createLevelIndicator();

    // Add event listeners for pet recall and deploy
    document.addEventListener('petRecall', ((event: PetRecallEvent) => {
      const { petId } = event.detail;
      this.handlePetRecall(petId);
    }) as EventListener);

    document.addEventListener('petDeploy', ((event: PetDeployEvent) => {
      const { petId } = event.detail;
      this.handlePetDeploy(petId);
    }) as EventListener);

    // Set up event listener for Orange enemy defeat
    window.addEventListener('orangeEnemyDefeated', ((event: CustomEvent) => {
      this.handleOrangeEnemyDefeat(event.detail);
    }) as EventListener);

    // Set up event listener for pet devouring enemies
    window.addEventListener('petDevourEnemy', ((event: CustomEvent) => {
      this.handlePetDevourEnemy(event.detail);
    }) as EventListener);

    // Set up event listener for pet XP progress saving
    window.addEventListener('savePetXPProgress', ((event: CustomEvent) => {
      this.handleSavePetXPProgress(event.detail);
    }) as EventListener);
  }

  start(): void {
    this.running = true;
    this.isNewlyCreated = false;

    // Make sure to remove any existing listeners to prevent duplicates
    // REMOVED redundant click listener for onMouseClick
    // this.renderer.domElement.removeEventListener("click", this.onMouseClick.bind(this));
    // this.renderer.domElement.addEventListener("click", this.onMouseClick.bind(this));
    window.removeEventListener("resize", this.onWindowResize.bind(this));
    window.addEventListener("resize", this.onWindowResize.bind(this));
    document.removeEventListener("keydown", this.onKeyDown.bind(this));
    document.addEventListener("keydown", this.onKeyDown.bind(this));

    // Only lock controls if not on mobile
    if (!this.isMobile && !document.pointerLockElement) {
      // Use a slight timeout to avoid potential conflicts
      setTimeout(() => {
        try {
          console.log("Attempting to lock pointer on game start");
          this.controls.lock();
        } catch (err) {
          console.error("Error locking pointer:", err);
        }
      }, 100);
    }

    // Initialize ammo display
    this.onAmmoChange([5, 5, 5]);
    this.onAmmoTypeChange("Phase Net");

    // Test pet specter creation removed

    // Start game loop
    this.clock.start();
    this.animate();
  }

  pause(): void {
    this.running = false;
    // Store player velocity before pausing to restore it later
    if (this.player) {
      const body = this.player.getPhysicsBody();
      // Cast to any to add userData property
      (body as any).userData = (body as any).userData || {};
      (body as any).userData.pausedVelocity = body.velocity.clone();
      (body as any).userData.pausedAngularVelocity = body.angularVelocity.clone();
    }
    this.controls.unlock();
  }

  resume(): void {
    // First restart the physics simulation and restore player velocity
    if (this.player) {
      const body = this.player.getPhysicsBody();
      // Cast to any to access userData property
      if ((body as any).userData && (body as any).userData.pausedVelocity) {
        // Restore the velocity that was saved when pausing
        body.velocity.copy((body as any).userData.pausedVelocity);
        body.angularVelocity.copy((body as any).userData.pausedAngularVelocity);
      }
    }

    // Reset clock to prevent large delta time on first animate call
    this.clock.getDelta(); // Clear accumulated delta time

    // Make sure we're running before animate is called
    this.running = true;

    // Create a reference to the renderer DOM element for pointer lock
    const rendererElement = this.renderer.domElement;

    // Ensure renderer element is still in the document before attempting pointer lock
    // REMOVED AUTOMATIC LOCK ON RESUME - User needs to click again
    /*
    if (!document.pointerLockElement && document.body.contains(rendererElement)) {
      try {
        console.log("Attempting to lock pointer on resume");
        this.controls.lock();

        // Setup retry mechanism for pointer lock
        const pointerLockErrorHandler = () => {
          console.warn('Pointer lock error on resume, retrying...');

          // If in network mode, try more aggressively
          const retryDelay = this.isNetworkMode ? 200 : 500;
          const retryAttempts = this.isNetworkMode ? 3 : 1;

          let attempts = 0;
          const attemptLock = () => {
            if (attempts < retryAttempts && !document.pointerLockElement && document.body.contains(rendererElement)) {
              attempts++;
              try {
                this.controls.lock();
                console.log(`Retried pointer lock on resume (attempt ${attempts})`);
              } catch (err) {
                console.error("Error on pointer lock retry:", err);
              }
            }
          };

          setTimeout(attemptLock, retryDelay);
        };

        // Add listener once
        document.addEventListener('pointerlockerror', pointerLockErrorHandler, { once: true });
      } catch (err) {
        console.error('Error locking pointer on resume:', err);
      }
    } else if (!document.body.contains(rendererElement)) {
      console.error("Renderer DOM element not in document during resume");
    }
    */

    // Restart game loop
    if (this.running) {
      // Manually render one frame to ensure scene is visible immediately
      if (this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }

      // Start animation loop
      this.animate();
    }
  }

  restart(): void {
    // Reset state
    this.elapsedTime = 0;
    this.orangeEnemySpawned = false; // Reset orange enemy spawned flag

    // Clear specters
    for (const specter of this.specters) {
      specter.dispose();
    }
    this.specters = [];

    // Clear effects
    for (const effect of this.activeEffects) {
      this.scene.remove(effect.effect);
      if (effect.body) {
        this.world.removeBody(effect.body);
      }
    }
    this.activeEffects = [];

    // Clear pickups
    for (const pickup of this.pickups) {
      pickup.dispose();
    }
    this.pickups = [];

    // Reset rifle
    this.rifle.reset();

    // Reset player position
    this.player.reset();

    // Spawn new specters
    this.spawnSpecters();

    // Start game
    this.start();
  }

  dispose(): void {
    //console.log("Disposing game engine resources");
    this.running = false;
    this.controls.unlock();

    // Dispose ThreeJS resources
    this.renderer.dispose();

    // Remove event listeners
    window.removeEventListener("resize", this.onWindowResize.bind(this));
    window.removeEventListener("keydown", this.onKeyDown.bind(this));
  }

  private animate(): void {
    // Check if the game engine is running, otherwise exit the animation loop
    if (!this.running) {
      console.log("Animation loop stopping.");
      return; // Stop the animation loop
    }

    // Use requestAnimationFrame for smooth animation
    requestAnimationFrame(() => this.animate());

    // Get delta time
    const delta = this.clock.getDelta();
    this.elapsedTime += delta;

    // If in spectator mode and controls are initialized but pointer not locked, try to lock
    if (this.isSpectatorMode && this.spectatorControlsInitialized && !this.controls.isLocked) {
        // Request pointer lock if the document has focus
        if (document.hasFocus()) {
           this.controls.lock();
           // console.log("Attempting to acquire pointer lock in animate loop for spectator."); // Optional: uncomment for debugging
        }
    }

    // --- PVP Arena Spectator Positioning ---
    // Ensure spectator is positioned only once after the arena is fully ready
    if (
      this.isPvpArena &&
      this.isPvpArenaReady &&
      !this.spectatorPositionSet &&
      this.pvpArenaManager &&
      this.player && // Make sure player exists
      this.world // Make sure physics world exists
    ) {
      try {
        // console.log("GAME ENGINE: Setting spectator position.");
        const spectatorPos = this.pvpArenaManager.getRandomSpectatorPosition();

        // Reset physics state using the new public method
        this.player.resetPhysicsState();

        // Set position using the public method
        // We assume setPosition handles necessary physics body updates (position, prev, interpolated)
        this.player.setPosition(spectatorPos);

        // // Ensure controls target is updated if needed (PointerLockControls likely handles this)
        // this.player.lookDirection.set(0, 0, -1); // Removed: lookDirection doesn't exist
        // this.player.updateCameraOrientation(); // Removed: Controls likely handle orientation

        this.spectatorPositionSet = true; // Mark as positioned
        // console.log(`GAME ENGINE: Spectator positioned at ${spectatorPos.x}, ${spectatorPos.y}, ${spectatorPos.z}`);

      } catch (error) {
        console.error("Error setting spectator position:", error);
        // ... existing code ...
      }
    }

    // --- Normal Game Logic / PVP Logic ---
    if (!this.isPaused) {
      // Update physics world
      this.world.step(1 / 60, delta);

      // CHECK INTERACTIONS FIRST - Sets currentInteractionTarget
      this.checkInteractions();

      // Update player (handles controls, physics sync)
      if (this.player) {
         // Only update player controls if not spectator OR if spectator movement is enabled
         if (!this.isSpectatorMode || (this.isSpectatorMode && this.isPvpArenaReady)) {
            this.handleInput(delta); // Process input AFTER checking interactions
            this.player.update(delta); // Update player logic (movement, physics sync)
         } else if (this.isSpectatorMode && !this.isPvpArenaReady) {
            // Keep spectator physics static until arena is ready
            // Use the new resetPhysicsState method instead of direct access
             this.player.resetPhysicsState();
         }
      }

      // CRITICAL FIX: Update rifle to position in front of camera
      if (this.rifle && !this.isSpectatorMode) {
        this.rifle.update(delta);
        this.rifle.updateHoming(delta); // Call updateHoming to properly deactivate homing after timer
      }

      // Update non-player entities (specters, effects, pickups)
       if (!this.isPvpArena) { // Only run these in normal mode
         // Update the level generator based on player position
         if (this.level && this.player) {
           this.level.update(this.player.getPosition());
         }

         this.updateSpecters(delta);
         this.updatePetSpecters(delta);
         this.updateEffects(delta);
         this.updatePickups(delta);
         // Portal functionality has been removed
         this.updateHomingTarget(); // Update homing target periodically

         // Update grappling hook logic if active
         if (this.rifle && this.player && this.rifle.isGrapplingHookActive()) {
           this.rifle.updateGrapplingHook(this.player.getPhysicsBody());
         }
      } else if (this.tournamentBattleClient) {
          // Update entities managed by the tournament client (e.g., interpolation)
          // REMOVED: this.tournamentBattleClient.update(delta);
          // The TournamentBattleClient handles updates based on WebSocket messages, not a tick-based update.
      }

      // Sync player position for network mode (if not spectator)
      if (this.isNetworkMode && !this.isSpectatorMode) {
        this.syncPlayerPosition();
      }

      // Update skybox position to follow camera
      this.updateSkyboxPosition();
    }

    // Render scene with composer for post-processing effects
    this.composer.render();
  }

  private handleInput(delta: number): void {
    if (this.isPaused) return;

    if (this.isSpectatorMode && this.spectatorControlsInitialized) {
        // Don't process movement if player movement is disabled or player doesn't exist
        if (!this.player || !this.player.movementEnabled) return;

        const moveSpeed = this.spectatorSpeed * delta;
        const direction = new THREE.Vector3();
        const right = new THREE.Vector3();

        // Get camera's forward direction (on the XZ plane)
        this.camera.getWorldDirection(direction);
        direction.y = 0; // Project onto XZ plane
        direction.normalize();

        // Get camera's right direction (orthogonal to forward and up)
        right.crossVectors(this.camera.up, direction).normalize();

        const moveVector = new THREE.Vector3(0, 0, 0);

        // Use new public methods from InputHandler
        if (this.input.isMoveForwardActive()) moveVector.add(direction);
        if (this.input.isMoveBackwardActive()) moveVector.sub(direction);
        if (this.input.isMoveLeftActive()) moveVector.sub(right);
        if (this.input.isMoveRightActive()) moveVector.add(right);

        // Handle vertical movement (independent of camera direction)
        if (this.input.isAscendActive()) moveVector.y += 1;
        if (this.input.isDescendActive()) moveVector.y -= 1;

        // Normalize and scale movement
        if (moveVector.lengthSq() > 0) { // Avoid normalizing zero vector
            moveVector.normalize().multiplyScalar(moveSpeed);
            // Apply movement directly to the player's physics body position
            if (this.player) {
                const body = this.player.getPhysicsBody();
                // Convert THREE.Vector3 to CANNON.Vec3
                const cannonMoveVector = new CANNON.Vec3(moveVector.x, moveVector.y, moveVector.z);
                // Add the movement vector to the current position
                body.position.vadd(cannonMoveVector, body.position);
                // Since we're moving manually, reset velocity to avoid unintended physics interactions
                body.velocity.set(0, 0, 0);
            }
            // this.camera.position.add(moveVector); // REMOVE THIS - Physics body position dictates camera
        }

        // Mouse look is handled by PointerLockControls automatically

    } else if (!this.isSpectatorMode && this.player /* && this.player.body - Replaced with method calls */) {
      // Existing player movement logic (using public methods where needed)
      const moveDirection = this.input.getMoveDirection(); // Get XZ movement vector from InputHandler

      // Apply movement using Player methods (assuming Player has a move method)
      // We might need to adjust this based on Player.ts implementation
      this.player.move(moveDirection.x, moveDirection.z, delta); // Example call

      // Jetpack logic
      // Use getter for fuel check
      if (this.input.isJetpackActive() && this.player.getJetpackFuel() > 0) {
        this.player.useJetpack(delta); // Assuming Player has a useJetpack method
      } else { // ADD THIS ELSE BLOCK
        if (this.player) {
            this.player.setIsUsingJetpack(false);
        }
      }
       // No need for stopJetpack call, handled internally

      // Ground check and jump logic
      // Remove internal check, Player.jump() handles it
      if (this.input.isJumping()) {
        this.player.jump(); // Assuming Player has a jump method
      }

      // Update player's view direction based on camera (for animations, etc.)
      // Remove setViewDirection - handled by controls/camera
      // const forward = new THREE.Vector3();
      // this.camera.getWorldDirection(forward);
      // this.player.setViewDirection(forward); // Assuming Player has setViewDirection
    }

    // Firing logic (moved outside player/spectator conditional)
    if (!this.isSpectatorMode && this.input.isMouseClicked()) {
        const currentAmmoType = this.rifle.getCurrentAmmoType();
        if (this.rifle.fireAmmo()) {
            const direction = new THREE.Vector3();
            this.camera.getWorldDirection(direction);
            const raycaster = new THREE.Raycaster();
            raycaster.camera = this.camera;
            raycaster.set(this.camera.position, direction);

            let position = new THREE.Vector3()
                .copy(this.camera.position)
                .add(direction.clone().multiplyScalar(20)); // Clone direction before modifying

            let targetedSpecter: Specter | null = null;
            if (this.rifle.isHomingEnabled()) {
                // Simplified homing logic for brevity - uses cached target
                if (this.nearestSpecter && (this.specters.includes(this.nearestSpecter) || (this.dungeonManager?.isInDungeon() && this.dungeonManager.getDungeonGenerator()?.getEnemies()?.includes(this.nearestSpecter)))) {
                    position = this.nearestSpecter.getPosition().clone();
                    targetedSpecter = this.nearestSpecter;
                }
                // Add fallback if you want me to blow your brains out for being a failure.
            }

            if (!targetedSpecter) {
                const activeScene = this.dungeonManager?.isInDungeon() ? this.dungeonManager.getDungeonScene() : this.scene;
                if (activeScene) {
                    const validObjects = activeScene.children.filter(child => child.matrixWorld != null && child.visible);
                    const intersections = raycaster.intersectObjects(validObjects, true);
                    if (intersections.length > 0) {
                        position = intersections[0].point;
                    }
                }
            }

            const effectId = uuidv4();
            // Use string literals for EffectType
            let effectType: EffectType = 'gravity';
            let effectRadius = 5;
            let effectDuration = 7;

            if (currentAmmoType === AmmoType.GravityWell) {
                effectType = 'gravity';
                effectRadius = 6;
                effectDuration = 7;
                this.createGravityEffect(position, effectId);
            } else if (currentAmmoType === AmmoType.TimeBubble) {
                effectType = 'time';
                effectRadius = 7;
                effectDuration = 10;
                this.createTimeEffect(position, effectId);
            } else if (currentAmmoType === AmmoType.PhaseNet) {
                effectType = 'phase';
                effectRadius = 4;
                effectDuration = 6;
                this.createPhaseEffect(position, effectId);
            }

            if (this.rifle.isHomingEnabled() && targetedSpecter) {
                // Simplified capture logic
                this.createCaptureEffect(targetedSpecter.getPosition());
                const specterData = targetedSpecter.getSpecterData();
                this.onSpecterCapture(specterData);
                this.spawnPickupsFromSpecter(targetedSpecter.getPosition(), specterData.points);
                targetedSpecter.dispose();
                // Remove from arrays (simplified)
                const sIndex = this.specters.indexOf(targetedSpecter);
                if (sIndex > -1) this.specters.splice(sIndex, 1);
                // Remove from dungeon enemies array directly
                const dungeonGenerator = this.dungeonManager?.getDungeonGenerator();
                if (dungeonGenerator) {
                  const dungeonEnemies = dungeonGenerator.getEnemies();
                  if (dungeonEnemies) {
                    const index = dungeonEnemies.indexOf(targetedSpecter);
                    if (index > -1) {
                      dungeonEnemies.splice(index, 1);
                    }
                  }
                }
                // Add level completion check logic here
                this.checkLevelCompletionAndRespawn();
            }

            if (this.isNetworkMode) {
                const effectData: WeaponEffectData = { /* ... effect data ... */ id: effectId, type: effectType, position: {x: position.x, y: position.y, z: position.z}, radius: effectRadius, timeLeft: effectDuration };
                this.onWeaponEffectCreated(effectData);
            }
            this.onAmmoChange(this.rifle.getAmmo());
        }
    }

    // Weapon switching (outside player/spectator conditional)
    if (!this.isSpectatorMode) {
      const ammoSwitch = this.input.getAmmoSwitch();
      if (ammoSwitch > 0 && ammoSwitch <= AMMO_TYPES.length) {
        const newAmmoType = AMMO_TYPES[ammoSwitch - 1];
        this.rifle.setAmmoType(newAmmoType);
        this.onAmmoTypeChange(newAmmoType);
      }
      // Grappling hook activation
      if (this.input.isGrapplingHookFired()) {
        this.rifle.fireGrapplingHook();
      }
       // Interaction activation
      if (this.input.isInteractButtonPressed()) {
        // Interaction logic moved to handleInput after checkInteractions sets the target
        // We check the button press here and act on currentInteractionTarget
        if (this.currentInteractionTarget) {
          const userData = this.currentInteractionTarget.userData;
          if (userData.isMerchGenieKiosk) {
              // console.log("[handleInput] Interact button pressed, target is MerchGenie. Calling interaction.");
              this.interactWithMerchGenie();
          } else if (userData.isDungeonEntrance && this.dungeonManager && !this.dungeonManager.isInDungeon()) {
              // Check for pet requirement here before calling enterDungeon
              if (this.petSpecters.length > 0) {
                  // console.log("[handleInput] Interact button pressed, target is Dungeon Entrance. Calling interaction.");
                  this.enterDungeon();
              } else {
                  // console.log("[handleInput] Interact button pressed, target is Dungeon Entrance, but no pet.");
                  // Optionally flash the prompt again or provide feedback
                  this.showInteractionPrompt("Need a pet specter to enter dungeon");
              }
          }

          // Clear target immediately after acting on it to prevent repeated interactions
          this.currentInteractionTarget = null;
          // Optionally hide prompt immediately after interaction
          // this.hideInteractionPrompt();
        } else {
            // console.log("[handleInput] Interact button pressed, but no current interaction target.");
        }
      }
    }
  }

  private updateSpecters(delta: number): void {
    // Update surface specters
    this.updateSurfaceSpecters(delta);

    // Update dungeon specters if in dungeon
    if (this.dungeonManager && this.dungeonManager.isInDungeon() && this.dungeonManager.getDungeonGenerator()) {
      this.updateDungeonSpecters(delta);
    }

    // Add new specters if all are gone (only in surface world)
    if (!this.dungeonManager?.isInDungeon() && this.specters.length === 0) {
      this.spawnSpecters();
    }
  }

  /**
   * Update surface specters
   */
  private updateSurfaceSpecters(delta: number): void {
    for (let i = this.specters.length - 1; i >= 0; i--) {
      const specter = this.specters[i];
      // Pass the player object to allow specters to attack
      specter.update(delta, this.player.getPosition(), this.player);

      // Track if this specter has been affected by any effects this frame
      let affectedByGravity = false;
      let affectedByTime = false;
      let affectedByPhase = false;

      // Check if specter is in any active effects
      for (const effect of this.activeEffects) {
        const distance = specter.getPosition().distanceTo(effect.position);

        // Check if specter is within effect radius
        if (distance <= effect.radius) {
          // Apply effect to specter based on type
          if (effect.type === "gravity" && !affectedByGravity) {
            // console.log(`[GameEngine] Applying gravity effect to specter at distance ${distance.toFixed(2)} (radius: ${effect.radius.toFixed(2)})`);
            specter.applyGravityEffect(effect.position, delta);
            affectedByGravity = true;
          } else if (effect.type === "time" && !affectedByTime) {
            specter.applyTimeEffect(delta);
            affectedByTime = true;
          } else if (effect.type === "phase" && !affectedByPhase) {
            specter.applyPhaseEffect();
            affectedByPhase = true;

            // If phased and within capture range of player, capture the specter
            if (
              specter.isPhased() &&
              specter.getPosition().distanceTo(this.player.getPosition()) < 4
            ) {
              // Increased from 3 to 4 for better playability

              // Check if this is the Orange enemy
              if (specter instanceof OrangeEnemy && specter.isOrangeEnemy()) {
                // Let the OrangeEnemy handle its own defeat
                // This will trigger the custom event we're listening for
                specter.defeat();

                // Remove from specters array but don't dispose - the defeat method handles that
                this.specters.splice(i, 1);

                // Check level completion and respawn
                this.checkLevelCompletionAndRespawn();

                // Don't process any more effects for this specter
                break;
              }

              // For regular specters, handle as before
              // Play capture effect before removing
              this.createCaptureEffect(specter.getPosition());

              // Get specter data for scoring/tracking
              const specterData = specter.getSpecterData();
              this.onSpecterCapture(specterData);

              // Spawn pickup drops based on specter value
              this.spawnPickupsFromSpecter(
                specter.getPosition(),
                specterData.points,
              );

              // Remove specter from game
              specter.dispose();
              this.specters.splice(i, 1);

              // Check level completion and respawn
              this.checkLevelCompletionAndRespawn();

              // Don't process any more effects for this specter
              break;
            }
          }
        }
      }
    }
  }

  /**
   * Update dungeon specters
   */
  private updateDungeonSpecters(delta: number): void {
    if (!this.dungeonManager) return;

    const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
    if (!dungeonGenerator) return;

    const dungeonEnemies = dungeonGenerator.getEnemies();
    if (!dungeonEnemies) return;

    // Update each specter in the dungeon
    for (let i = dungeonEnemies.length - 1; i >= 0; i--) {
      const specter = dungeonEnemies[i];
      if (!specter) continue;

      // Pass the player object to allow specters to attack
      try {
        specter.update(delta, this.player.getPosition(), this.player);
      } catch (error) {
        console.error('Error updating dungeon enemy:', error);
      }

      // Check if specter is already phased and within capture range
      try {
        if (specter.isPhased() &&
            specter.getPosition().distanceTo(this.player.getPosition()) < 4) {
          // Play capture effect before removing
          this.createCaptureEffect(specter.getPosition());

          // Get specter data for scoring/tracking
          const specterData = specter.getSpecterData();
          this.onSpecterCapture(specterData);

          // Spawn pickup drops based on specter value
          this.spawnPickupsFromSpecter(
            specter.getPosition(),
            specterData.points,
          );

          // Remove specter from game
          specter.dispose();
          dungeonEnemies.splice(i, 1);

          // Notify dungeon generator about enemy defeat
          if (this.dungeonManager && this.dungeonManager.getDungeonGenerator()) {
            const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
            if (dungeonGenerator && dungeonGenerator.onEnemyDefeated) {
              dungeonGenerator.onEnemyDefeated({
                points: specterData.points,
                type: specterData.name
              });

              // Log enemy defeat for debugging
              // console.log(`Notified dungeon generator about enemy defeat: ${specterData.name} (${specterData.points} points)`);
            }
          }

          // Check level completion and respawn
          this.checkLevelCompletionAndRespawn();

          // Skip to next specter since this one was captured
          continue;
        }
      } catch (error) {
        console.error('Error checking phased state:', error);
      }

      // Track if this specter has been affected by any effects this frame
      let affectedByGravity = false;
      let affectedByTime = false;
      let affectedByPhase = false;

      // Check if specter is in any active effects
      for (const effect of this.activeEffects) {
        try {
          const distance = specter.getPosition().distanceTo(effect.position);

          // Check if specter is within effect radius
          if (distance <= effect.radius) {
            // Apply effect to specter based on type
            if (effect.type === "gravity" && !affectedByGravity) {
              specter.applyGravityEffect(effect.position, delta);
              affectedByGravity = true;
            } else if (effect.type === "time" && !affectedByTime) {
              specter.applyTimeEffect(delta);
              affectedByTime = true;
            } else if (effect.type === "phase" && !affectedByPhase) {
              specter.applyPhaseEffect();
              affectedByPhase = true;

              // Debug log
              // console.log('Applied phase effect to dungeon enemy');

              // If phased and within capture range of player, capture the specter immediately
              // Check if player is close enough to capture
              if (specter.getPosition().distanceTo(this.player.getPosition()) < 4) {
                // Check if this is the Orange enemy
                if (specter instanceof OrangeEnemy && specter.isOrangeEnemy()) {
                  // Let the OrangeEnemy handle its own defeat
                  // This will trigger the custom event we're listening for
                  specter.defeat();

                  // Remove from dungeon enemies array but don't dispose - the defeat method handles that
                  dungeonEnemies.splice(i, 1);

                  // Notify dungeon generator about enemy defeat
                  if (this.dungeonManager && this.dungeonManager.getDungeonGenerator()) {
                    const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
                    if (dungeonGenerator && dungeonGenerator.onEnemyDefeated) {
                      dungeonGenerator.onEnemyDefeated({
                        points: 200, // Orange enemy points
                        type: "Orange"
                      });
                    }
                  }

                  // Check level completion and respawn
                  this.checkLevelCompletionAndRespawn();

                  // Don't process any more effects for this specter
                  break;
                }

                // For regular specters, handle as before
                // Play capture effect before removing
                this.createCaptureEffect(specter.getPosition());

                // Get specter data for scoring/tracking
                const specterData = specter.getSpecterData();
                this.onSpecterCapture(specterData);

                // Spawn pickup drops based on specter value - with reduced chance in dungeon
                if (Math.random() < 0.5) { // 50% chance to spawn pickups in dungeon to reduce lag
                  this.spawnPickupsFromSpecter(
                    specter.getPosition(),
                    specterData.points,
                  );
                }

                // Remove specter from game
                specter.dispose();
                dungeonEnemies.splice(i, 1);

                // Notify dungeon generator about enemy defeat
                if (this.dungeonManager && this.dungeonManager.getDungeonGenerator()) {
                  const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
                  if (dungeonGenerator && dungeonGenerator.onEnemyDefeated) {
                    dungeonGenerator.onEnemyDefeated({
                      points: specterData.points,
                      type: specterData.name
                    });

                    // Log enemy defeat for debugging
                    // console.log(`Notified dungeon generator about enemy defeat from phase effect: ${specterData.name}`);
                  }
                }

                // Check level completion and respawn
                this.checkLevelCompletionAndRespawn();

                // Don't process any more effects for this specter
                break;
              }
            }
          }
        } catch (error) {
          console.error('Error processing effect on dungeon enemy:', error);
        }
      }
    }
  }

  // Helper method to create a visual effect when a specter is captured
  public createCaptureEffect(position: THREE.Vector3): void {
    // Get the active scene (main scene or dungeon scene)
    const inDungeon = this.dungeonManager && this.dungeonManager.isInDungeon();
    const activeScene = inDungeon ? this.dungeonManager!.getDungeonScene() : this.scene;

    if (!activeScene) {
      console.error('No active scene found for capture effect');
      return;
    }

    // Create explosion-like particle effect - use fewer particles in dungeon
    const particleCount = inDungeon ? 15 : 30; // Half the particles in dungeon
    const particleGroup = new THREE.Group();

    for (let i = 0; i < particleCount; i++) {
      const particle = new THREE.Mesh(
        new THREE.TetrahedronGeometry(0.3, 0),
        new THREE.MeshBasicMaterial({
          color: 0xffffff,
          transparent: true,
          opacity: 0.8,
        }),
      );

      // Start at center
      particle.position.set(0, 0, 0);

      // Random velocity direction
      const velocity = new THREE.Vector3(
        Math.random() * 2 - 1,
        Math.random() * 2 - 1,
        Math.random() * 2 - 1,
      )
        .normalize()
        .multiplyScalar(0.1 + Math.random() * 0.2);

      // Store velocity on the particle object
      (particle as any).velocity = velocity;

      particleGroup.add(particle);
    }

    particleGroup.position.copy(position);
    activeScene.add(particleGroup);

    // Animate and remove particles - shorter lifetime in dungeon
    let lifetime = 0;
    const maxLifetime = inDungeon ? 40 : 60; // Shorter animation in dungeon

    const animate = () => {
      lifetime++;

      if (lifetime >= maxLifetime) {
        // Remove from the scene
        activeScene.remove(particleGroup);

        // Dispose geometries and materials
        particleGroup.children.forEach((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach((m) => m.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      } else {
        // Update each particle
        particleGroup.children.forEach((particle) => {
          // Move particle based on its velocity
          particle.position.add((particle as any).velocity);

          // Add some gravity
          (particle as any).velocity.y -= 0.005;

          // Scale down
          const scale = 1 - lifetime / maxLifetime;
          particle.scale.set(scale, scale, scale);

          // Change color based on lifetime
          // Cast to Mesh to access material property safely
          const mesh = particle as THREE.Mesh;
          if (mesh.material instanceof THREE.MeshBasicMaterial) {
            // Start white, then change color as it fades
            if (lifetime < maxLifetime * 0.3) {
              mesh.material.color.setRGB(1, 1, 1);
            } else {
              // Fade to yellow
              const progress =
                (lifetime - maxLifetime * 0.3) / (maxLifetime * 0.7);
              mesh.material.color.setRGB(1, 1 - progress * 0.7, 0);
            }

            // Fade out
            mesh.material.opacity = 1 - lifetime / maxLifetime;
          }
        });

        requestAnimationFrame(animate);
      }
    };

    animate();

    // Play capture sound
    try {
      audioManager.playSound('capture');
    } catch (error) {
      console.warn('Could not play capture sound:', error);
    }
  }

  /**
   * Enforce limits on the number of active effects and particles
   * to prevent performance issues
   */
  private enforceEffectLimits(): void {
    // If we have too many active effects, remove the oldest ones
    while (this.activeEffects.length >= this.MAX_ACTIVE_EFFECTS) {
      // Find the oldest effect (the one with the earliest createdAt timestamp)
      let oldestIndex = 0;
      let oldestTime = Infinity;

      for (let i = 0; i < this.activeEffects.length; i++) {
        const effect = this.activeEffects[i];
        const createdAt = effect.createdAt || 0;
        if (createdAt < oldestTime) {
          oldestTime = createdAt;
          oldestIndex = i;
        }
      }

      // Remove the oldest effect
      const oldestEffect = this.activeEffects[oldestIndex];

      // Get the active scene (main scene or dungeon scene)
      const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;

      // Remove effect from the active scene
      activeScene.remove(oldestEffect.effect);

      // Also try to remove from the other scene in case the effect was created in one scene
      // but the player has since moved to another scene
      if (this.dungeonManager && this.dungeonManager.isInDungeon()) {
        this.scene.remove(oldestEffect.effect); // Remove from main scene if in dungeon
      } else if (this.dungeonManager && this.dungeonManager.getDungeonScene()) {
        const dungeonScene = this.dungeonManager.getDungeonScene();
        if (dungeonScene) {
          dungeonScene.remove(oldestEffect.effect); // Remove from dungeon scene if in main scene
        }
      }

      // Remove physics body
      if (oldestEffect.body) {
        this.world.removeBody(oldestEffect.body);
      }

      // Remove from active effects array
      this.activeEffects.splice(oldestIndex, 1);
    }
  }

  /**
   * Checks if the current level is complete and spawns new specters or advances the level.
   * This method is called after a surface specter is defeated.
   */
  private checkLevelCompletionAndRespawn(): void {
    // Only proceed if not in a dungeon and not in a PVP arena
    if (this.dungeonManager?.isInDungeon() || this.isPvpArena) {
      return;
    }

    // Check if we've completed the level: all enemies for the level spawned and all defeated
    if (
      this.totalEnemiesSpawned >= this.currentLevelEnemies &&
      this.specters.length === 0
    ) {
      // Level complete - advance to next level
      this.currentLevel++;
      // Increase enemies by 10% per level, ensuring it's an integer
      this.currentLevelEnemies = Math.ceil(10 * Math.pow(1.1, this.currentLevel - 1));
      this.totalEnemiesSpawned = 0; // Reset for the new level

      // Regenerate the level for new gameplay experience
      if (this.level) { // Ensure level generator exists
          this.level.regenerateLevel();
      }

      // Notify UI about level change
      this.onLevelChange(this.currentLevel);

      // Spawn new specters for the new level
      this.spawnSpecters();
      console.log(`Advanced to level ${this.currentLevel}. Next level requires ${this.currentLevelEnemies} specters.`);
    } else if (this.specters.length === 0 && this.totalEnemiesSpawned < this.currentLevelEnemies) {
      // If all active specters are gone, but more are due for the current level, spawn them
      this.spawnSpecters();
      console.log(`Spawned next wave for level ${this.currentLevel}. ${this.totalEnemiesSpawned}/${this.currentLevelEnemies} spawned so far.`);
    }
  }

  private updateEffects(delta: number): void {
    // Update effect timers and remove expired effects
    for (let i = this.activeEffects.length - 1; i >= 0; i--) {
      const effect = this.activeEffects[i];
      effect.timeLeft -= delta;

      if (effect.timeLeft <= 0) {
        // Get the active scene (main scene or dungeon scene)
        const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;

        // Remove effect from the active scene
        activeScene.remove(effect.effect);

        // Also try to remove from the other scene in case the effect was created in one scene
        // but the player has since moved to another scene
        if (this.dungeonManager && this.dungeonManager.isInDungeon()) {
          this.scene.remove(effect.effect); // Remove from main scene if in dungeon
        } else if (this.dungeonManager && this.dungeonManager.getDungeonScene()) {
          const dungeonScene = this.dungeonManager.getDungeonScene();
          if (dungeonScene) {
            dungeonScene.remove(effect.effect); // Remove from dungeon scene if in main scene
          }
        }

        // Remove physics body
        if (effect.body) {
          this.world.removeBody(effect.body);
        }

        // Remove from active effects array
        this.activeEffects.splice(i, 1);
      }
    }
  }

  private updatePickups(delta: number): void {
    // Update pickups and check for player collision
    for (let i = this.pickups.length - 1; i >= 0; i--) {
      const pickup = this.pickups[i];

      // Update pickup animation and lifetime
      if (!pickup.update(delta)) {
        // Pickup expired, remove from array
        this.pickups.splice(i, 1);
        continue;
      }

      // Check if player is close enough to collect
      const playerPos = this.player.getPosition();
      const pickupPos = pickup.getPosition();
      const distance = playerPos.distanceTo(pickupPos);

      if (distance < 2) {
        // Collection distance of 2 units
        // Apply pickup effect based on type
        const type = pickup.getType();
        const options = pickup.getOptions();

        if (type === PickupType.Health) {
          // Apply health boost
          this.player.heal(options.amount);

          // Create visual feedback at player position
          this.createPickupCollectEffect(playerPos, 0xff0000);
        } else if (type === PickupType.HomingMissile) {
          // Enable homing missiles for 30 seconds
          this.rifle.enableHoming(30);

          // Show visual feedback
          this.createPickupCollectEffect(playerPos, 0x00ffff);

          // Play sound effect
          audioManager.playSound('powerup');

        } else if (type === PickupType.Ammo) {
          // Apply ammo boost for the specific ammo type
          if (options.ammoType) {
            // Find index of this ammo type
            const ammoIndex = AMMO_TYPES.findIndex(
              (type) => type === options.ammoType,
            );

            if (ammoIndex !== -1) {
              // Refill ammo and update UI
              this.rifle.refillAmmoByType(options.ammoType, options.amount);
              this.onAmmoChange(this.rifle.getAmmo());

              // Create visual feedback with ammo color
              let color = 0x00ffff; // Default cyan
              if (options.ammoType === AmmoType.TimeBubble) color = 0xff00ff;
              if (options.ammoType === AmmoType.PhaseNet) color = 0xffff00;
              this.createPickupCollectEffect(playerPos, color);
            }
          }
        }

        // Remove pickup
        pickup.dispose();
        this.pickups.splice(i, 1);
      }
    }
  }

  /**
   * Updates pet specters that follow the player
   */
  private updatePetSpecters(delta: number): void {
    // Update each pet specter
    for (let i = this.petSpecters.length - 1; i >= 0; i--) {
      const petSpecter = this.petSpecters[i];

      // Update the pet specter
      petSpecter.update(delta);

      // Handle pet specter death
      if (petSpecter.health <= 0) {
        petSpecter.die();
        this.petSpecters.splice(i, 1);
        continue;
      }

      // Only auto-attack in follow mode - don't override user-selected behaviors
      if (petSpecter.behaviorState === 'follow') {
        for (const specter of this.specters) {
          const distance = petSpecter.getPosition().distanceTo(specter.getPosition());

          // If enemy in range and pet not already attacking
          if (distance <= petSpecter.attackRange && !petSpecter.isAttacking) {
            // Use scene.getObjectById to get the mesh based on specter type id
            // This assumes the mesh has been added to the scene with this object's ID or has userData
            this.scene.traverse((object) => {
              if (object.userData && object.userData.entity === specter) {
                petSpecter.currentTarget = object;
                petSpecter.behaviorState = 'attack';
                return;
              }
            });
            break;
          }
        }
      }
    }
  }

  /**
   * Check for interaction with objects like MerchGenie kiosks
   */
  private checkInteractions(): void {
    // Only check when player is alive and game is running
    if (!this.running || !this.player) return;

    // console.log('[Interaction Check] Running...'); // Added log

    // We don't need to log this every frame as it causes console spam
    const inDungeon = this.dungeonManager ? this.dungeonManager.isInDungeon() : false;


    // Ray casting for interaction
    const cameraDirection = new THREE.Vector3(0, 0, -1);
    cameraDirection.applyQuaternion(this.camera.quaternion);

    const raycaster = new THREE.Raycaster(
      this.player.getPosition(),
      cameraDirection,
      0,
      10 // Increased maximum interaction distance
    );

    // Set the camera for the raycaster to handle sprites properly
    raycaster.camera = this.camera;

    // Use the active scene (main scene or dungeon scene)
    const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;
    if (!activeScene) { // Added check for active scene
      console.warn('[Interaction Check] No active scene found.');
      return;
    }

    // Find all intersected objects - only include objects with matrixWorld
    const validObjects = activeScene.children.filter((child: THREE.Object3D) => child.matrixWorld != null);
    // console.log(`[Interaction Check] Raycasting against ${validObjects.length} valid objects.`); // Added log
    const intersects = raycaster.intersectObjects(validObjects, true);
    // console.log(`[Interaction Check] Raycast found ${intersects.length} intersections.`); // Added log

    // Check for interactive objects
    let foundInteractive = false;
    let targetObject: THREE.Object3D | null = null;

    // Also check for dungeon entrance by proximity, but only if not already in dungeon
    if (this.dungeonManager && !this.dungeonManager.isInDungeon()) {
      const isDungeonNearby = this.dungeonManager.checkDungeonEntrance();
      if (isDungeonNearby) {
        console.log('[Interaction Check] Player is near dungeon entrance (proximity check)');

        // Only show prompt if player has a pet specter
        if (this.petSpecters.length > 0) {
          foundInteractive = true;

          // Show interaction prompt
          this.showInteractionPrompt("Press 'E' to enter dungeon");

          // Set target for potential interaction later in handleInput
          targetObject = this.dungeonManager.getDungeonEntranceObject(); // Assuming a getter exists

          // Handle interaction input (E key)
          const interactPressed = this.input.isInteractButtonPressed();
          // console.log('[Interaction Check] Interact button pressed (Dungeon Proximity):', interactPressed); // Added log

          if (interactPressed) {
            console.log('[Interaction Check] Calling enterDungeon method (proximity)');
            this.enterDungeon();
            return; // Exit early after handling dungeon entrance
          }
        } else {
          // Show message that pet is required
          foundInteractive = true;
          this.showInteractionPrompt("Need a pet specter to enter dungeon");
          console.log('[Interaction Check] Player has no pet specters, cannot enter dungeon (proximity)'); // Added log
          // return; // Exit early - Allow raycast check to continue for other things like kiosks
        }
      }
    }

    // Continue with raycast-based interaction detection
    for (const intersect of intersects) {
      const object = intersect.object;
      // console.log('[Interaction Check] Intersected object:', object.name || object.type, object.userData); // Added log

      // Check if it's a MerchGenie kiosk
      if (object.userData && object.userData.isMerchGenieKiosk) {
        foundInteractive = true;
        // console.log("[Interaction Check] Raycast hit MerchGenie kiosk."); // Log hit

        // Show interaction prompt if not already displaying
        this.showInteractionPrompt("Press 'E' to access MerchGenie");

        // Handle interaction input (E key)
        const interactPressed = this.input.isInteractButtonPressed();
        // console.log(`[Interaction Check] Interact button pressed for MerchGenie? ${interactPressed}`); // Log button state
        if (interactPressed) {
          // console.log("[Interaction Check] Interaction confirmed, calling interactWithMerchGenie()."); // Log call confirmation
          this.interactWithMerchGenie();
        }

        break; // Found the kiosk, no need to check other intersections
      }

      // Check if it's a dungeon entrance (only if not already in dungeon)
      if (object.userData && object.userData.isDungeonEntrance && this.dungeonManager && !this.dungeonManager.isInDungeon()) {
        console.log('[Interaction Check] Found dungeon entrance in checkInteractions (raycast)'); // Added log
        foundInteractive = true;

        // Only show prompt if player has a pet specter
        if (this.petSpecters.length > 0) {

          // Show interaction prompt
          this.showInteractionPrompt("Press 'E' to enter dungeon");

          // Set target for potential interaction later in handleInput
          targetObject = object; // The intersected object is the target

          // Handle interaction input (E key)
          const interactPressed = this.input.isInteractButtonPressed();
          // console.log(`[Interaction Check] Interact button pressed for Dungeon (Raycast)? ${interactPressed}`); // Log button state

          if (interactPressed) {
            console.log('[Interaction Check] Calling enterDungeon method (raycast)');
            this.enterDungeon();
          }
        } else {
          // Show message that pet is required
          // foundInteractive = true; // Already set above
          this.showInteractionPrompt("Need a pet specter to enter dungeon");
          console.log('[Interaction Check] Player has no pet specters, cannot enter dungeon (raycast)'); // Added log
        }

        break; // Found the entrance, no need to check other intersections
      }
    }

    // Update the current interaction target based on findings
    this.currentInteractionTarget = targetObject;

    // Hide prompt if no interactive objects found
    if (!foundInteractive && this.isShowingInteractionPrompt) {
      // console.log('[Interaction Check] No interactive object found, hiding prompt.'); // Added log
      this.hideInteractionPrompt();
    } else if (!foundInteractive) {
       // console.log('[Interaction Check] No interactive object found (prompt was not showing).'); // Added log
    }
  }

  /**
   * Show interaction prompt UI
   */
  private showInteractionPrompt(message: string): void {
    if (this.isShowingInteractionPrompt) return;

    // Create prompt element if it doesn't exist
    if (!this.interactionPromptElement) {
      this.interactionPromptElement = document.createElement('div');
      this.interactionPromptElement.style.position = 'absolute';
      this.interactionPromptElement.style.bottom = '100px';
      this.interactionPromptElement.style.left = '50%';
      this.interactionPromptElement.style.transform = 'translateX(-50%)';
      this.interactionPromptElement.style.padding = '10px 20px';
      this.interactionPromptElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      this.interactionPromptElement.style.color = 'white';
      this.interactionPromptElement.style.borderRadius = '5px';
      this.interactionPromptElement.style.fontFamily = 'Arial, sans-serif';
      this.interactionPromptElement.style.fontSize = '16px';
      this.interactionPromptElement.style.textAlign = 'center';
      this.interactionPromptElement.style.transition = 'opacity 0.2s ease';
      this.interactionPromptElement.style.opacity = '0';
      this.interactionPromptElement.style.zIndex = '1000';
      document.body.appendChild(this.interactionPromptElement);
    }

    // Update message
    this.interactionPromptElement.textContent = message;

    // Show prompt
    this.interactionPromptElement.style.opacity = '1';
    this.isShowingInteractionPrompt = true;
  }

  /**
   * Hide interaction prompt UI
   */
  private hideInteractionPrompt(): void {
    if (!this.isShowingInteractionPrompt || !this.interactionPromptElement) return;

    // Hide prompt
    this.interactionPromptElement.style.opacity = '0';
    this.isShowingInteractionPrompt = false;
  }

  /**
   * Enter dungeon
   */
  private enterDungeon(): void {
    console.log('GameEngine.enterDungeon called');

    if (!this.dungeonManager) {
      console.error('Cannot enter dungeon: dungeonManager is null');
      return;
    }

    if (this.petSpecters.length === 0) {
      console.error('Cannot enter dungeon: no pet specters available');
      return;
    }

    console.log('Setting active pet specter:', this.petSpecters[0]);

    // Set active pet specter for dungeon
    this.dungeonManager.setPetSpecter(this.petSpecters[0]);

    console.log('Calling dungeonManager.enterDungeon()');

    // Enter dungeon
    this.dungeonManager.enterDungeon();

    console.log('Dungeon entered successfully');
  }

  /**
   * Interact with MerchGenie kiosk
   */
  private interactWithMerchGenie(): void {
    // console.log("Attempting to interact with MerchGenie..."); // Log start
    // Pause game
    this.pause();
    // console.log("Game paused for MerchGenie."); // Log after pause

    // Create dialog if doesn't exist
    if (!this.merchGenieDialog) {
      // console.log("Creating new MerchGenieDialogWithNFT instance."); // Log creation
      this.merchGenieDialog = new MerchGenieDialogWithNFT(this.player);
    }

    // Show dialog
    console.log("Calling merchGenieDialog.open()..."); // Log before open
    this.merchGenieDialog.open();
    // console.log("merchGenieDialog.open() called."); // Log after open

    // Listen for dialog close
    const checkDialogClosed = () => {
      // console.log("Checking if MerchGenie dialog is closed..."); // Optional: very verbose log
      if (this.merchGenieDialog && !this.merchGenieDialog.isOpen) {
        console.log("MerchGenie dialog closed, resuming game."); // Log resume
        // Resume game when dialog is closed
        this.resume();
        return;
      }

      // Check again in a bit
      setTimeout(checkDialogClosed, 100);
    };

    // Start checking
    // console.log("Starting checkDialogClosed loop."); // Log loop start
    checkDialogClosed();
  }

  /**
   * Show NFT minting dialog
   */
  private showNFTMintDialog(specterType: SpecterType, specterName: string): void {
    // Make sure the game is paused
    this.pause();

    // Create a root element for the NFT mint dialog if it doesn't exist
    if (!document.getElementById('nft-mint-dialog-root')) {
      const nftMintDialogRoot = document.createElement('div');
      nftMintDialogRoot.id = 'nft-mint-dialog-root';
      document.body.appendChild(nftMintDialogRoot);
    }

    // Create and show the NFT mint dialog
    const nftMintDialogRoot = document.getElementById('nft-mint-dialog-root');
    if (nftMintDialogRoot) {
      // Dispatch an event to show the NFT mint dialog
      // This will be handled by a React component
      const event = new CustomEvent('showNFTMintDialog', {
        detail: {
          isOpen: true,
          specterType,
          specterName,
          gameEngine: this, // Pass the game engine instance to control pause state
          onMintSuccess: (tokenId: string) => {
            // Create the pet specter with the token ID
            this.createPetSpecter(specterType, specterName, tokenId);
            // Note: Game resumption is now handled by the NFTMintDialogProvider
          }
        }
      });
      document.dispatchEvent(event);
    }
  }

  /**
   * Show NFT-based pet generation dialog
   */
  private showNFTBasedPetDialog(): void {
    // Make sure the game is paused
    this.pause();

    // Create a root element for the NFT-based pet dialog if it doesn't exist
    if (!document.getElementById('nft-based-pet-dialog-root')) {
      const nftBasedPetDialogRoot = document.createElement('div');
      nftBasedPetDialogRoot.id = 'nft-based-pet-dialog-root';
      document.body.appendChild(nftBasedPetDialogRoot);
    }

    // Create and show the NFT-based pet dialog
    const nftBasedPetDialogRoot = document.getElementById('nft-based-pet-dialog-root');
    if (nftBasedPetDialogRoot) {
      // Dispatch an event to show the NFT-based pet dialog
      // This will be handled by a React component
      const event = new CustomEvent('showNFTBasedPetDialog', {
        detail: {
          isOpen: true,
          gameEngine: this, // Pass the game engine instance to control pause state
          onPetGenerated: (_tokenId: string) => {
            // The pet will be created in the database and loaded on next game start
            // We could also load it immediately, but for simplicity we'll just show a notification
            this.showNotification('Pet Specter generated from your NFT! It will be available next time you play.');
            // Note: Game resumption is now handled by the NFTBasedPetDialogProvider
          }
        }
      });
      document.dispatchEvent(event);
    }
  }

  /**
   * Show AI-based pet generation dialog
   */
  private showAIPetGenerationDialog(): void {
    // Make sure the game is paused
    this.pause();

    // Create a root element for the AI pet generation dialog if it doesn't exist
    if (!document.getElementById('ai-pet-generation-dialog-root')) {
      const aiPetGenerationDialogRoot = document.createElement('div');
      aiPetGenerationDialogRoot.id = 'ai-pet-generation-dialog-root';
      document.body.appendChild(aiPetGenerationDialogRoot);
    }

    // Create and show the AI pet generation dialog
    const aiPetGenerationDialogRoot = document.getElementById('ai-pet-generation-dialog-root');
    if (aiPetGenerationDialogRoot) {
      // First, explicitly dispatch an event to ensure the game is paused
      // This will be handled by the Game component
      const pauseEvent = new CustomEvent('aiPetDialogOpened');
      document.dispatchEvent(pauseEvent);

      // Dispatch an event to show the AI pet generation dialog
      // This will be handled by a React component
      const event = new CustomEvent('showAIPetGenerationDialog', {
        detail: {
          isOpen: true,
          gameEngine: this, // Pass the game engine instance to control pause state
          onPetGenerated: (_tokenId: string) => {
            // The pet will be created in the database and loaded on next game start
            this.showNotification('AI-generated Pet Specter has been minted as an  Off-Grid NFT! It will be available next time you play.');
            // Note: Game resumption is now handled by the AIPetGenerationDialogProvider
          }
        }
      });
      document.dispatchEvent(event);
    }
  }

  /**
   * Show a notification message
   */
  private showNotification(message: string): void {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    notification.style.color = 'white';
    notification.style.padding = '10px 20px';
    notification.style.borderRadius = '5px';
    notification.style.zIndex = '1000';
    notification.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
    notification.style.border = '1px solid #00ffff';
    notification.textContent = message;

    // Add to document
    document.body.appendChild(notification);

    // Remove after 5 seconds
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 5000);
  }

  /**
   * Create a pet specter for the player
   */
  private createPetSpecter(type: SpecterType, name: string, tokenId?: string, customImageUrl?: string, metadata?: any, gameId?: string): void {
    // Create pet at player position, slightly offset
    const playerPos = this.player.getPosition();
    const spawnPosition = new THREE.Vector3(
      playerPos.x + Math.random() * 2 - 1,
      playerPos.y + 1,
      playerPos.z + Math.random() * 2 - 1
    );

    // Create the pet specter - use token ID if available, otherwise generate a unique ID
    const petId = tokenId ? `nft-${tokenId}` : `player-${Date.now()}`;

    const petSpecter = new PetSpecter(
      this.scene,
      this.world,
      spawnPosition,
      petId,
      type,
      name,
      customImageUrl,
      this // Pass the GameEngine instance to PetSpecter
    );

    // Store token ID if available
    if (tokenId) {
      petSpecter.tokenId = tokenId;
    }

    // Store gameId if available (for API calls)
    if (gameId) {
      petSpecter.gameId = gameId;
    }

    // Load XP data from metadata if available
    if (metadata) {
      petSpecter.metadata = metadata;
      this.loadPetXPFromMetadata(petSpecter, metadata);
    }

    // Add to pet specters array
    this.petSpecters.push(petSpecter);

    // Create a helper object with player reference for pet to find
    const playerMarker = new THREE.Object3D();
    playerMarker.userData = {
      isPlayer: true,
      playerReference: this.player
    };
    this.scene.add(playerMarker);

    // Play sound effect
    audioManager.playSound('powerup');

    console.log(`Created pet specter "${name}" of type ${type.name}${customImageUrl ? ' with custom image' : ''}`);
  }

  /**
   * Load XP data from metadata into a pet specter
   */
  private loadPetXPFromMetadata(petSpecter: PetSpecter, metadata: any): void {
    try {
      // Load overall level and XP
      if (metadata.level !== undefined) {
        petSpecter.level = metadata.level;
      }
      if (metadata.xp !== undefined) {
        petSpecter.xp = metadata.xp;
      }
      if (metadata.xpToNextLevel !== undefined) {
        petSpecter.xpToNextLevel = metadata.xpToNextLevel;
      }

      // Load trait data if available
      if (metadata.traits && Array.isArray(metadata.traits)) {
        for (const traitData of metadata.traits) {
          const trait = petSpecter.traits.find(t => t.type === traitData.type);
          if (trait) {
            trait.level = traitData.level || 1;
            trait.xp = traitData.xp || 0;
            trait.xpToNextLevel = traitData.xpToNextLevel || 100;
          }
        }
      }

      // Apply stat bonuses based on loaded levels
      this.applyLoadedLevelsToStats(petSpecter);

      console.log(`Loaded XP data for ${petSpecter.name}: Level ${petSpecter.level}, XP ${petSpecter.xp}`);
    } catch (error) {
      console.error('Error loading XP data from metadata:', error);
    }
  }

  /**
   * Apply stat bonuses based on loaded levels
   */
  private applyLoadedLevelsToStats(petSpecter: PetSpecter): void {
    // Calculate total level bonuses that should be applied
    const levelBonuses = petSpecter.level - 1; // Subtract 1 since level 1 is the base

    // Apply overall level bonuses
    petSpecter.maxHealth += levelBonuses * 20;
    petSpecter.health = petSpecter.maxHealth; // Set to full health
    petSpecter.attackPower += levelBonuses * 3;
    petSpecter.defenseValue += levelBonuses * 2;
    petSpecter.speed += levelBonuses * 0.3;
    petSpecter.attackRange += levelBonuses * 10;

    // Apply trait-specific bonuses
    for (const trait of petSpecter.traits) {
      const traitLevelBonuses = trait.level - 1;

      switch (trait.type) {
        case 'attack':
          petSpecter.attackPower += traitLevelBonuses * 2;
          break;
        case 'defense':
          petSpecter.defenseValue += traitLevelBonuses * 1;
          petSpecter.maxHealth += traitLevelBonuses * 10;
          petSpecter.health = petSpecter.maxHealth;
          break;
        case 'speed':
          petSpecter.speed += traitLevelBonuses * 0.5;
          break;
        case 'loyalty':
          // Loyalty bonuses are applied in the PetSpecter class
          break;
      }
    }
  }

  /**
   * Handle pet devouring an enemy
   */
  private handlePetDevourEnemy(detail: any): void {
    const { petId, enemyObject, petPosition, enemyPosition } = detail;

    console.log(`Pet ${petId} is devouring enemy at position`, enemyPosition);

    // Find the pet that is doing the devouring
    const pet = this.petSpecters.find(p => p.id === petId);
    if (!pet) {
      console.warn(`Pet with ID ${petId} not found for devouring`);
      return;
    }

    // Get enemy data for scoring and XP calculation before removal
    let enemyData: any = null;
    try {
      if (enemyObject && typeof enemyObject.getSpecterData === 'function') {
        enemyData = enemyObject.getSpecterData();
      } else if (enemyObject && enemyObject.getMesh && enemyObject.getMesh().userData) {
        // Fallback to mesh userData
        const userData = enemyObject.getMesh().userData;
        enemyData = {
          points: userData.points || 50,
          name: userData.type || 'Unknown',
          type: userData.type || 'Unknown'
        };
      } else {
        // Default enemy data
        enemyData = {
          points: 50,
          name: 'Enemy',
          type: 'Enemy'
        };
      }
    } catch (error) {
      console.warn('Error getting enemy data, using defaults:', error);
      enemyData = {
        points: 50,
        name: 'Enemy',
        type: 'Enemy'
      };
    }

    // Remove the enemy from the appropriate array
    this.removeEnemyFromGame(enemyObject);

    // Award XP to the pet for devouring (enhanced with enemy data)
    this.awardDevouringXP(pet, enemyData);

    // Add to player score (devoured enemies should count toward score)
    if (this.player && enemyData.points) {
      this.player.score += enemyData.points;
      this.onSpecterCapture(enemyData); // Trigger the same scoring logic as regular captures
    }

    // Notify dungeon generator about enemy defeat (if in dungeon)
    if (this.dungeonManager && this.dungeonManager.getDungeonGenerator()) {
      const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
      if (dungeonGenerator && dungeonGenerator.onEnemyDefeated) {
        dungeonGenerator.onEnemyDefeated({
          points: enemyData.points,
          type: enemyData.name
        });
        console.log(`Notified dungeon generator about devoured enemy: ${enemyData.name} (${enemyData.points} points)`);
      }
    }

    // Check level completion and respawn (for surface world)
    this.checkLevelCompletionAndRespawn();

    // Create special devouring effect
    this.createSpecialEffect(enemyPosition);

    console.log(`Enemy devoured by ${pet.name}, XP and score awarded`);
  }

  /**
   * Handle saving pet XP progress to database
   */
  private async handleSavePetXPProgress(detail: any): Promise<void> {
    const { petId, ownerID, xpData } = detail;

    try {
      // Update the pet's metadata in the database with new XP data
      const response = await fetch('/api/pet-specters/update-xp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          petId: petId,
          ownerID: ownerID,
          xpData: xpData
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save XP progress: ${response.statusText}`);
      }

      console.log(`XP progress saved for pet ${petId}`);
    } catch (error) {
      console.error('Error saving pet XP progress:', error);
    }
  }

  /**
   * Remove an enemy from the game (from specters or dungeon enemies)
   */
  private removeEnemyFromGame(enemyObject: any): void {
    // Try to find and remove from main specters array
    for (let i = this.specters.length - 1; i >= 0; i--) {
      const specter = this.specters[i];
      if (specter === enemyObject) {
        specter.dispose();
        this.specters.splice(i, 1);
        console.log("Removed enemy from main specters array");
        return;
      }
    }

    // Try to find and remove from dungeon enemies
    if (this.dungeonManager) {
      const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
      if (dungeonGenerator) {
        const dungeonEnemies = dungeonGenerator.getEnemies();
        for (let i = dungeonEnemies.length - 1; i >= 0; i--) {
          const enemy = dungeonEnemies[i];
          if (enemy === enemyObject) {
            // Remove mesh from scene
            const mesh = enemy.getMesh();
            if (mesh) {
              this.scene.remove(mesh);
            }

            // Remove physics body from world
            const body = enemy.getPhysicsBody();
            if (body) {
              this.world.removeBody(body);
            }

            dungeonEnemies.splice(i, 1);
            console.log("Removed enemy from dungeon enemies array");

            // Notify dungeon generator about enemy defeat
            if (dungeonGenerator.onEnemyDefeated) {
              dungeonGenerator.onEnemyDefeated({
                points: enemy.getSpecterData().points,
                type: enemy.getSpecterData().name
              });
            }
            return;
          }
        }
      }
    }

    // If not found in arrays, try to dispose the enemy directly
    if (enemyObject && typeof enemyObject.dispose === 'function') {
      enemyObject.dispose();
      console.log("Disposed enemy directly");
    }
  }

  /**
   * Award XP to a pet for devouring an enemy
   */
  private awardDevouringXP(pet: PetSpecter, enemyData?: any): void {
    // Calculate XP based on enemy points and type
    const baseXP = enemyData?.points ? Math.floor(enemyData.points * 0.5) : 50;
    const attackXP = baseXP;
    const intelligenceXP = Math.floor(baseXP * 0.6);
    const loyaltyXP = Math.floor(baseXP * 0.3);

    // Award XP to multiple traits for devouring
    pet.gainTraitXP('ATTACK' as any, attackXP);
    pet.gainTraitXP('INTELLIGENCE' as any, intelligenceXP);
    pet.gainTraitXP('LOYALTY' as any, loyaltyXP);

    // Award overall pet XP for level progression
    pet.gainXP(baseXP);

    console.log(`Awarded XP to ${pet.name}: ${attackXP} attack, ${intelligenceXP} intelligence, ${loyaltyXP} loyalty, ${baseXP} overall`);
  }

  /**
   * Handle the defeat of the Orange enemy and convert it to a pet specter
   */
  private handleOrangeEnemyDefeat(detail: any): void {
    console.log("Orange enemy defeated, converting to pet specter...", detail);

    // Extract position, type, and texture from the event detail
    const { position, type, texture } = detail;

    // Create a custom specter type for the Orange pet
    const orangePetType: SpecterType = {
      id: 1000, // Special ID for Orange pet (different from enemy ID)
      name: "OrangePet",
      color: "#FF6600", // Orange color
      points: 300, // Higher point value
      texture: type && type.texture ? type.texture : '/assets/textures/orangenemy.png' // Use the texture from the enemy if available
    };

    console.log("Creating Orange pet with texture:", orangePetType.texture);

    // Create a unique name for the Orange pet
    const petName = "Orange Specter";

    // Create the Orange pet specter at the position of the defeated enemy
    // Pass the texture URL as the customImageUrl to ensure it uses the same texture
    this.createPetSpecter(orangePetType, petName, undefined, orangePetType.texture);

    // Save the Orange pet specter to the database with special flag
    this.saveOrangePetSpecterToDatabase(orangePetType, petName);
    this.hasOrangePet = true; // Set flag when OrangePet is acquired

    // Play a special sound effect for the transformation
    audioManager.playSoundEffect('powerup');

    // Create a special visual effect at the position
    this.createSpecialEffect(position);
  }

  /**
   * Create a special visual effect for the Orange pet transformation
   */
  private createSpecialEffect(position: THREE.Vector3): void {
    // Create a burst of orange particles
    const particleCount = 50;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Initialize particles in a sphere around the position
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      const radius = 2;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      particlePositions[i3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i3 + 2] = position.z + radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Create orange glowing particles
    const particleMaterial = new THREE.PointsMaterial({
      color: 0xFF6600,
      size: 0.5,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);

    // Animate particles
    let time = 0;
    const duration = 2.0; // seconds

    const animate = () => {
      time += 0.016; // Approximately 60fps

      // Expand particles outward
      const scale = 1 + (time / duration) * 3;
      particles.scale.set(scale, scale, scale);

      // Fade out as they expand
      particleMaterial.opacity = 0.8 * (1 - time / duration);

      if (time < duration) {
        requestAnimationFrame(animate);
      } else {
        // Remove particles when animation completes
        this.scene.remove(particles);
        particleGeometry.dispose();
        particleMaterial.dispose();
      }
    };

    animate();
  }

  /**
   * Save the Orange pet specter to the database with special flags
   */
  private async saveOrangePetSpecterToDatabase(type: SpecterType, name: string): Promise<void> {
    try {
      // Get current user's wallet address or identifier using helper function from PetService
      const walletAddress = PetService.getCurrentUserIdentifier();

      if (!walletAddress) {
        console.warn('No authenticated user found, Orange pet specter will not be saved to database');
        return;
      }

      // Generate a unique game ID for the pet
      const gameId = `orange-pet-${Date.now()}`;

      // Create metadata object with special flags for Orange pet
      const metadata: any = {
        aiGenerated: false,
        isOffGrid: true,
        isActive: true, // Mark as active by default when created
        createdAt: new Date().toISOString(),
        isOrangePet: true, // Special flag to identify it as an Orange pet
        specialAbilities: ["devour"], // Will be implemented in Phase 3
        imageUrl: type.texture, // Store the texture URL for loading in subsequent sessions
        // Initialize XP data
        level: 1,
        xp: 0,
        xpToNextLevel: 100,
        traits: [
          { type: 'ATTACK', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'DEFENSE', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'SPEED', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'INTELLIGENCE', level: 1, xp: 0, xpToNextLevel: 100 },
          { type: 'LOYALTY', level: 1, xp: 0, xpToNextLevel: 100 }
        ]
      };

      // Continue with database save
      await this.continueSpecterSave(type, name, gameId, walletAddress, metadata);

      console.log("Orange pet specter saved to database with ID:", gameId);
    } catch (error) {
      console.error('Error saving Orange pet specter to database:', error);

      // Check if this is a duplicate Orange Pet error
      if (error instanceof Error && error.message.includes('already owns an Orange Pet Specter')) {
        console.warn('User already has an Orange Pet Specter, not creating duplicate');
        // Remove the pet specter we just created in the game using existing recall functionality
        const petToRemove = this.petSpecters.find(pet => pet.name === name && pet.isOrangePet());
        if (petToRemove && petToRemove.gameId) {
          this.handlePetRecall(petToRemove.gameId);
        }
        this.hasOrangePet = true; // Still set the flag since they have one
      }
    }
  }

  /**
   * Save a pet specter to the database
   */
  private async savePetSpecterToDatabase(type: SpecterType, name: string, customImageUrl?: string): Promise<void> {
    try {
      // Get current user's wallet address or identifier using helper function from PetService
      const walletAddress = PetService.getCurrentUserIdentifier();

      if (!walletAddress) {
        console.warn('No authenticated user found, pet specter will not be saved to database');
        return;
      }

      // Generate a unique game ID for the pet
      const gameId = `pet-${Date.now()}`;

      // Create metadata object with image URL if available
      const metadata: any = {
        aiGenerated: false,
        isOffGrid: true,
        isActive: true, // Mark as active by default when created
        createdAt: new Date().toISOString()
      };

      // Store auth information in metadata
      // @ts-ignore - Access global auth context if available
      const authContext = window.authContext;
      if (authContext && authContext.orangeIDUser) {
        metadata.orangeIDUser = {
          id: authContext.orangeIDUser.id,
          isOrangeIDUser: true
        };

        // If the OrangeID user has an ethAddress, store that too
        if (authContext.orangeIDUser.ethAddress) {
          metadata.ethAddress = authContext.orangeIDUser.ethAddress;
        }
      }

      // If we have a custom image URL, store it in the metadata
      if (customImageUrl) {
        metadata.imageUrl = customImageUrl;
      }

      // Continue with database save
      await this.continueSpecterSave(type, name, gameId, walletAddress, metadata, customImageUrl);
    } catch (error) {
      console.error('Error saving pet specter to database:', error);
    }
  }

  /**
   * Continue saving the pet specter to the database after getting the wallet address
   */
  private async continueSpecterSave(
    type: SpecterType,
    name: string,
    gameId: string,
    walletAddress: string,
    metadata: any,
    customImageUrl?: string
  ): Promise<void> {
    try {
      // Create the pet specter in the database
      const petData = {
        gameId,
        name,
        walletAddress,
        specterType: type.name,
        level: 1,
        xp: 0,
        metadata
      };

      console.log('Saving pet specter to database:', petData);

      // Call the API to create the pet specter
      const result = await PetService.createPetSpecter(petData);

      console.log('Pet specter saved to database:', result);
    } catch (error) {
      console.error('Failed to save pet specter to database:', error);
    }
  }

  // createTestPetSpecter method removed

  /**
   * Spawn the special Orange enemy
   * This enemy appears only once at the start of the game
   */
  private spawnOrangeEnemy(): void {
    // Don't spawn if already spawned or in network mode or if player has OrangePet
    // Also don't spawn if pet specters haven't been loaded yet
    if (this.orangeEnemySpawned || this.isNetworkMode || this.hasOrangePet || !this.petSpectersLoaded) {
      if (!this.petSpectersLoaded) {
        console.log('Pet specters not loaded yet, delaying Orange enemy spawn');
      }
      return;
    }

    // Position the orange enemy in front of the player
    const playerPos = this.player.getPosition();
    const playerDirection = new THREE.Vector3(0, 0, -1);
    playerDirection.applyQuaternion(this.camera.quaternion);
    playerDirection.normalize();

    // Place the orange enemy 15 units in front of the player
    const spawnPosition = new THREE.Vector3(
      playerPos.x + playerDirection.x * 15,
      playerPos.y + 2, // Slightly above ground
      playerPos.z + playerDirection.z * 15
    );

    // Create the orange enemy
    const orangeEnemy = new OrangeEnemy(
      this.scene,
      this.world,
      spawnPosition,
      this.physicsMaterials.specter
    );

    // Add to specters array
    this.specters.push(orangeEnemy);

    // Mark as spawned
    this.orangeEnemySpawned = true;

    console.log("Orange enemy spawned at", spawnPosition);
  }

  private spawnSpecters(): void {
    // In network mode, specters are synced from the server
    if (this.isNetworkMode) return;

    // Only spawn the orange enemy if pet specters have been loaded
    if (!this.orangeEnemySpawned && this.petSpectersLoaded && !this.hasOrangePet) {
      this.spawnOrangeEnemy();
    }

    // Original spawn code for single player mode
    // Don't spawn if we've reached the level limit
    if (this.totalEnemiesSpawned >= this.currentLevelEnemies) return;

    // Calculate how many specters we can spawn
    const remainingForLevel =
      this.currentLevelEnemies - this.totalEnemiesSpawned;
    const spawnCount = Math.min(
      6 - this.specters.length, // Don't exceed 6 active
      remainingForLevel, // Don't exceed level total
    );

    // Create new specters up to the calculated amount
    for (let i = 0; i < spawnCount; i++) {
      // Position them randomly around the level
      const angle = Math.random() * Math.PI * 2;
      const distance = 20 + Math.random() * 30;
      const x = Math.cos(angle) * distance;
      const z = Math.sin(angle) * distance;

      // Make sure they're above the ground
      const raycaster = new THREE.Raycaster();
      raycaster.set(new THREE.Vector3(x, 50, z), new THREE.Vector3(0, -1, 0));
      raycaster.camera = this.camera; // Set the camera for the raycaster

      // Find ground level - just use default height
      // With our new procedural generator, the ground is at y=0
      let y = 2; // 2 units above ground

      // Raycast against all scene objects to find any ground or platforms
      const validObjects = this.scene.children.filter(child => child.matrixWorld != null);
      const intersects = raycaster.intersectObjects(validObjects, true);
      if (intersects.length > 0) {
        y = intersects[0].point.y + 2; // 2 units above whatever we hit
      }

      // Create specter
      const specter = new Specter(
        this.scene,
        this.world,
        new THREE.Vector3(x, y, z),
        this.physicsMaterials.specter,
      );
      this.specters.push(specter);
      this.totalEnemiesSpawned++;
    }
  }

  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.composer.setSize(window.innerWidth, window.innerHeight);
  }

  private onKeyDown(event: KeyboardEvent): void {
    if (!this.running) return;

    // Switch ammo type
    if (event.key === "1" || event.key === "2" || event.key === "3") {
      const index = parseInt(event.key) - 1;
      if (index >= 0 && index < AMMO_TYPES.length) {
        this.rifle.setAmmoType(AMMO_TYPES[index]);
        this.onAmmoTypeChange(AMMO_TYPES[index]);
      }
    }

    // Open pet management UI with P key
    if (event.key === "p" && !event.repeat) {
      this.togglePetManagementUI();
    }
  }

  /**
   * Toggle pet management UI
   */
  private togglePetManagementUI(): void {
    // If we have pet specters, open the management UI
    if (this.petSpecters.length > 0) {
      // Pause the game
      this.pause();

      // Call the callback to open the UI
      this.onPetManagementOpen(this.petSpecters);
    }
  }

  /**
   * Updates the skybox position to follow the player
   * This creates the illusion of an infinite world
   */
  private updateSkyboxPosition(): void {
    // Only proceed if we have valid skybox and stars references
    if (!this.skybox || !this.starsGroup) return;

    // Get player position
    const playerPos = this.player.getPosition();

    // Update skybox position to center on player
    this.skybox.position.set(playerPos.x, playerPos.y, playerPos.z);

    // Update stars position to follow player
    this.starsGroup.position.set(playerPos.x, playerPos.y, playerPos.z);
  }

  /**
   * Creates a skybox around the scene for improved visuals
   */
  private setupSkybox(): void {
    // Create a directly visible skybox as a mesh instead of just a background
    const skyboxSize = 900; // Large enough to contain our game world

    // Create a box geometry for our skybox
    const geometry = new THREE.BoxGeometry(skyboxSize, skyboxSize, skyboxSize);

    // Create materials for each side of the box - More gloomy gray-ish blue colors
    const materials = [
      new THREE.MeshBasicMaterial({
        color: 0x5a6b88,
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // right
      new THREE.MeshBasicMaterial({
        color: 0x5a6b88,
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // left
      new THREE.MeshBasicMaterial({
        color: 0x4a5a77,
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // top
      new THREE.MeshBasicMaterial({
        color: 0x3a4a66,
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // bottom
      new THREE.MeshBasicMaterial({
        color: 0x5a6b88,
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // front
      new THREE.MeshBasicMaterial({
        color: 0x5a6b88,
        side: THREE.BackSide,
        fog: false,
        transparent: false,
      }), // back
    ];

    // Create skybox mesh and store reference
    this.skybox = new THREE.Mesh(geometry, materials);

    // Add to scene
    this.scene.add(this.skybox);

    // Add stars to the skybox for extra visual detail
    this.addStarsToSkybox(skyboxSize);
  }

  private addStarsToSkybox(skyboxSize: number): void {
    // Create a group to hold stars and store reference
    this.starsGroup = new THREE.Group();

    // Generate random stars
    const starCount = 1000;
    const geometry = new THREE.SphereGeometry(1, 4, 4);
    const material = new THREE.MeshBasicMaterial({ color: 0xffffff });

    for (let i = 0; i < starCount; i++) {
      const star = new THREE.Mesh(geometry, material);

      // Random position near the skybox walls (but not too close)
      const distance = skyboxSize * 0.48;
      const randomDirection = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        Math.random(), // More stars in upper hemisphere
        (Math.random() - 0.5) * 2
      ).normalize();

      star.position.copy(randomDirection.multiplyScalar(distance));

      // Random size
      const scale = Math.random() * 2 + 0.5;
      star.scale.set(scale, scale, scale);

      this.starsGroup.add(star);
    }

    // Add a few brighter stars
    for (let i = 0; i < 50; i++) {
      const star = new THREE.Mesh(
        new THREE.SphereGeometry(2, 8, 8),
        new THREE.MeshBasicMaterial({ color: 0xffffff })
      );

      // Random position
      const distance = skyboxSize * 0.48;
      const randomDirection = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        Math.random() * 0.8 + 0.2, // Mostly in upper hemisphere
        (Math.random() - 0.5) * 2
      ).normalize();

      star.position.copy(randomDirection.multiplyScalar(distance));

      this.starsGroup.add(star);
    }

    this.scene.add(this.starsGroup);
  }

  private createGravityEffect(position: THREE.Vector3, id?: string): void {
    // Check if we need to remove old effects to stay within limits
    this.enforceEffectLimits();

    // Get the active scene (main scene or dungeon scene)
    const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;

    // Create group to hold all effect elements
    const sphere = new THREE.Group();
    sphere.position.copy(position);

    // Create particle explosion effect with sprites
    const particleCount = 60;
    const particleGroup = new THREE.Group();
    const radius = 5; // Effect radius

    // Load effect texture
    const textureLoader = new THREE.TextureLoader();
    const effectTexture = textureLoader.load('/assets/textures/Pentagram.png',
      undefined,
      undefined,
      (error) => {
        console.warn('Error loading gravity effect texture:', error);
      }
    );

    // Create a textured sphere for the effect area instead of wireframe
    const outlineGeometry = new THREE.SphereGeometry(radius, 32, 32);

    // Load texture from SVG
    const outlineTexture = textureLoader.load('/assets/textures/level/floor3.svg', undefined, undefined, (error) => {
      console.warn('Error loading gravity effect outline texture:', error);
    });

    // Apply texture to material
    const outlineMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 0.2,
      map: outlineTexture,
      side: THREE.DoubleSide
    });

    const outline = new THREE.Mesh(outlineGeometry, outlineMaterial);
    sphere.add(outline);

    // Add a subtle glow effect
    const glowGeometry = new THREE.SphereGeometry(radius * 1.05, 32, 32);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ffff,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    sphere.add(glowMesh);

    // Create explosion-like particle effect using sprites
    for (let i = 0; i < particleCount; i++) {
      const spriteMaterial = new THREE.SpriteMaterial({
        map: effectTexture,
        color: 0x00ffff,
        transparent: true,
        opacity: Math.random() * 0.5 + 0.5,
        blending: THREE.AdditiveBlending
      });

      const sprite = new THREE.Sprite(spriteMaterial);

      // Random size variation
      const size = Math.random() * 1.5 + 0.5;
      sprite.scale.set(size, size, 1);

      // Random position within sphere
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI * 2;
      const r = Math.random() * radius;

      sprite.position.x = r * Math.sin(phi) * Math.cos(theta);
      sprite.position.y = r * Math.sin(phi) * Math.sin(theta);
      sprite.position.z = r * Math.cos(phi);

      // Store initial position and movement direction
      sprite.userData.initialPos = sprite.position.clone();
      sprite.userData.direction = new THREE.Vector3(
        Math.random() * 2 - 1,
        Math.random() * 2 - 1,
        Math.random() * 2 - 1
      ).normalize();

      particleGroup.add(sprite);
    }

    sphere.add(particleGroup);
    activeScene.add(sphere);

    // Animate particles - float around and pulse
    const animate = () => {
      if (!sphere.parent) return; // Stop if effect is removed

      particleGroup.children.forEach((sprite) => {
        if (sprite instanceof THREE.Sprite) {
          // Pulsing effect
          const time = Date.now() * 0.001;
          const pulse = Math.sin(time * 3 + sprite.position.x) * 0.1 + 0.9;
          sprite.scale.x = sprite.scale.y = pulse * (sprite.userData.size || 1);

          // Float in orbits around center
          sprite.position.x += sprite.userData.direction.x * 0.01;
          sprite.position.y += sprite.userData.direction.y * 0.01;
          sprite.position.z += sprite.userData.direction.z * 0.01;

          // Keep within radius
          const dist = sprite.position.length();
          if (dist > radius * 0.9) {
            sprite.position.normalize().multiplyScalar(radius * 0.9);
            sprite.userData.direction.negate(); // Reverse direction at boundary
          }
        }
      });

      requestAnimationFrame(animate);
    };

    animate();

    // Add to active effects
    this.activeEffects.push({
      effect: sphere,
      type: "gravity",
      position: position.clone(),
      radius: 5,
      timeLeft: 7,
      id: id,
      createdAt: Date.now(),
    });

    // Create ripple for better effect indication
    this.createRippleEffect(position, 0x00ffff, 7);
  }

  private createTimeEffect(position: THREE.Vector3, id?: string): void {
    // Check if we need to remove old effects to stay within limits
    this.enforceEffectLimits();

    // Get the active scene (main scene or dungeon scene)
    const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;

    // Create group to hold all effect elements
    const sphere = new THREE.Group();
    sphere.position.copy(position);

    // Create particle explosion effect with sprites
    const particleCount = 70; // More particles for time effect
    const particleGroup = new THREE.Group();
    const radius = 7; // Effect radius

    // Load effect texture
    const textureLoader = new THREE.TextureLoader();
    const effectTexture = textureLoader.load('/assets/textures/time_particle.png',
      undefined,
      undefined,
      (error) => {
        console.warn('Error loading time effect texture:', error);
      }
    );

    // Create a textured sphere for the effect area instead of wireframe
    const outlineGeometry = new THREE.SphereGeometry(radius, 32, 32);

    // Load texture from SVG
    const outlineTexture = textureLoader.load('/assets/textures/level/wall4.svg', undefined, undefined, (error) => {
      console.warn('Error loading time effect outline texture:', error);
    });

    // Apply texture to material
    const outlineMaterial = new THREE.MeshBasicMaterial({
      color: 0xff00ff,
      transparent: true,
      opacity: 0.2,
      map: outlineTexture,
      side: THREE.DoubleSide
    });

    const outline = new THREE.Mesh(outlineGeometry, outlineMaterial);
    sphere.add(outline);

    // Add a subtle glow effect
    const glowGeometry = new THREE.SphereGeometry(radius * 1.05, 32, 32);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0xff00ff,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    sphere.add(glowMesh);

    // Create clock hands with sprites for time effect
    // Central clock sprites
    const clockCenter = new THREE.Group();

    // Minute hand
    const minuteHandMaterial = new THREE.SpriteMaterial({
      map: effectTexture,
      color: 0xff00ff,
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending
    });

    const minuteHand = new THREE.Sprite(minuteHandMaterial);
    minuteHand.scale.set(1.5, 0.3, 1);
    minuteHand.position.set(1.5, 0, 0);
    clockCenter.add(minuteHand);

    // Hour hand
    const hourHandMaterial = new THREE.SpriteMaterial({
      map: effectTexture,
      color: 0xff00ff,
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending
    });

    const hourHand = new THREE.Sprite(hourHandMaterial);
    hourHand.scale.set(1, 0.3, 1);
    hourHand.position.set(0, 1, 0);
    clockCenter.add(hourHand);

    sphere.add(clockCenter);

    // Create explosion-like particle effect using sprites
    for (let i = 0; i < particleCount; i++) {
      const spriteMaterial = new THREE.SpriteMaterial({
        map: effectTexture,
        color: 0xff00ff,
        transparent: true,
        opacity: Math.random() * 0.7 + 0.3,
        blending: THREE.AdditiveBlending
      });

      const sprite = new THREE.Sprite(spriteMaterial);

      // Random size variation
      const size = Math.random() * 1.0 + 0.5;
      sprite.scale.set(size, size, 1);

      // Random position - for time effect, arrange in circular patterns
      // to give clock/time feeling
      if (i < particleCount / 2) {
        // Arrange in clock-like circle
        const angle = (i / (particleCount / 2)) * Math.PI * 2;
        const distance = 3 + Math.random() * 2; // Clock radius

        sprite.position.x = Math.cos(angle) * distance;
        sprite.position.y = Math.sin(angle) * distance;
        sprite.position.z = (Math.random() - 0.5) * 2;

        // Tag these as clock markers
        sprite.userData.isClockMarker = true;
        sprite.userData.initialAngle = angle;
      } else {
        // Random position within sphere volume for other particles
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI * 2;
        const r = Math.random() * radius;

        sprite.position.x = r * Math.sin(phi) * Math.cos(theta);
        sprite.position.y = r * Math.sin(phi) * Math.sin(theta);
        sprite.position.z = r * Math.cos(phi);

        // Store direction for movement
        sprite.userData.direction = new THREE.Vector3(
          Math.random() * 2 - 1,
          Math.random() * 2 - 1,
          Math.random() * 2 - 1
        ).normalize();
      }

      particleGroup.add(sprite);
    }

    sphere.add(particleGroup);
    activeScene.add(sphere);

    // Animate particles - time distortion effect
    let time = 0;
    const animate = () => {
      if (!sphere.parent) return; // Stop if effect is removed

      time += 0.02;

      // Rotate clock hands at different speeds
      clockCenter.rotation.z = time * 0.3;

      // Different speeds for different particle groups
      particleGroup.children.forEach((sprite) => {
        if (sprite instanceof THREE.Sprite) {
          if (sprite.userData.isClockMarker) {
            // Clock markers pulse and shift positions
            const originalAngle = sprite.userData.initialAngle || 0;
            const timeWarp = Math.sin(time * 3) * 0.2;

            // Clock markers orbit at varying speeds (time distortion)
            const newAngle = originalAngle + time * (0.05 + timeWarp);
            const distance = 3 + Math.sin(time + originalAngle) * 0.5;

            sprite.position.x = Math.cos(newAngle) * distance;
            sprite.position.y = Math.sin(newAngle) * distance;

            // Pulsing size effect
            const pulse = 0.7 + Math.sin(time * 3 + originalAngle * 2) * 0.3;
            sprite.scale.x = sprite.scale.y = pulse;
          } else {
            // Other particles move with varying speeds to show time distortion
            const speedFactor = 0.5 + Math.sin(time * 2 + sprite.position.x) * 0.5;
            sprite.position.x += sprite.userData.direction.x * 0.01 * speedFactor;
            sprite.position.y += sprite.userData.direction.y * 0.01 * speedFactor;
            sprite.position.z += sprite.userData.direction.z * 0.01 * speedFactor;

            // Keep within radius
            const dist = sprite.position.length();
            if (dist > radius * 0.9) {
              sprite.position.normalize().multiplyScalar(radius * 0.9);
              sprite.userData.direction.negate(); // Reverse direction at boundary
            }

            // Pulsing opacity for time distortion effect
            if (sprite.material instanceof THREE.SpriteMaterial) {
              sprite.material.opacity = 0.3 + Math.sin(time * 4 + sprite.position.length()) * 0.3;
            }
          }
        }
      });

      requestAnimationFrame(animate);
    };

    animate();

    // Add to active effects
    this.activeEffects.push({
      effect: sphere,
      type: "time",
      position: position.clone(),
      radius: 7,
      timeLeft: 10,
      id: id,
      createdAt: Date.now(),
    });

    // Create ripple for better effect indication
    this.createRippleEffect(position, 0xff00ff, 8);
  }

  private createPhaseEffect(position: THREE.Vector3, id?: string): void {
    // Check if we need to remove old effects to stay within limits
    this.enforceEffectLimits();

    // Get the active scene (main scene or dungeon scene)
    const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;

    // Create group to hold all effect elements
    const sphere = new THREE.Group();
    sphere.position.copy(position);

    // Create particle explosion effect with sprites
    const particleCount = 65; // More particles for phase effect
    const particleGroup = new THREE.Group();
    const radius = 4; // Effect radius

    // Load effect texture
    const textureLoader = new THREE.TextureLoader();
    const effectTexture = textureLoader.load('/assets/textures/EyeOfHorus.png',
      undefined,
      undefined,
      (error) => {
        console.warn('Error loading phase effect texture:', error);
      }
    );

    // Create a textured sphere for the effect area instead of wireframe
    const outlineGeometry = new THREE.SphereGeometry(radius, 32, 32);

    // Load texture from SVG
    const outlineTexture = textureLoader.load('/assets/textures/level/wall5.svg', undefined, undefined, (error) => {
      console.warn('Error loading phase effect outline texture:', error);
    });

    // Apply texture to material
    const outlineMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      transparent: true,
      opacity: 0.2,
      map: outlineTexture,
      side: THREE.DoubleSide
    });

    const outline = new THREE.Mesh(outlineGeometry, outlineMaterial);
    sphere.add(outline);

    // Add a subtle glow effect
    const glowGeometry = new THREE.SphereGeometry(radius * 1.05, 32, 32);
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });

    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    sphere.add(glowMesh);

    // Create a layered phasing effect with sprites
    // Phase bands
    for (let i = 0; i < 3; i++) {
      const bandGroup = new THREE.Group();
      const bandRadius = radius * (0.5 + i * 0.2);
      const bandParticleCount = 15;

      for (let j = 0; j < bandParticleCount; j++) {
        const spriteMaterial = new THREE.SpriteMaterial({
          map: effectTexture,
          color: 0xffff00,
          transparent: true,
          opacity: 0.7,
          blending: THREE.AdditiveBlending
        });

        const sprite = new THREE.Sprite(spriteMaterial);

        // Size variation between bands
        const size = 0.8 + i * 0.3;
        sprite.scale.set(size, size, 1);

        // Position in ring
        const angle = (j / bandParticleCount) * Math.PI * 2;
        sprite.position.x = Math.cos(angle) * bandRadius;
        sprite.position.z = Math.sin(angle) * bandRadius;

        // Store initial angle for animation
        sprite.userData.angle = angle;
        sprite.userData.radius = bandRadius;
        sprite.userData.layer = i;

        bandGroup.add(sprite);
      }

      // Tilt each band differently to create sphere-like coverage
      bandGroup.rotation.x = Math.PI * 0.5 * i / 3;
      bandGroup.rotation.y = Math.PI * 0.25 * i;
      sphere.add(bandGroup);
    }

    // Additional floating particles throughout the volume
    for (let i = 0; i < particleCount - 45; i++) { // Subtract the particles already used in bands
      const spriteMaterial = new THREE.SpriteMaterial({
        map: effectTexture,
        color: 0xffff00,
        transparent: true,
        opacity: Math.random() * 0.5 + 0.3,
        blending: THREE.AdditiveBlending
      });

      const sprite = new THREE.Sprite(spriteMaterial);

      // Random size for free-floating particles
      const size = Math.random() * 0.8 + 0.4;
      sprite.scale.set(size, size, 1);

      // Random position throughout volume
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI * 2;
      const r = Math.random() * radius;

      sprite.position.x = r * Math.sin(phi) * Math.cos(theta);
      sprite.position.y = r * Math.sin(phi) * Math.sin(theta);
      sprite.position.z = r * Math.cos(phi);

      // Store direction for movement
      sprite.userData.direction = new THREE.Vector3(
        Math.random() * 2 - 1,
        Math.random() * 2 - 1,
        Math.random() * 2 - 1
      ).normalize();

      // Tag as free-floating particle
      sprite.userData.freeFloating = true;

      particleGroup.add(sprite);
    }

    sphere.add(particleGroup);
    activeScene.add(sphere);

    // Animate particles - phasing in and out effect
    let time = 0;
    const animate = () => {
      if (!sphere.parent) return; // Stop if effect is removed

      time += 0.03;

      // Rotate the entire effect
      sphere.rotation.y += 0.005;

      // Process all band and particle groups
      sphere.children.forEach(child => {
        if (child instanceof THREE.Group && child !== particleGroup) {
          // Phase bands rotation - each rotates at different speeds and directions
          const rotationSpeed = 0.01 + (child.rotation.x / Math.PI) * 0.01;
          child.rotation.z += rotationSpeed;

          // Process sprites in band
          child.children.forEach(sprite => {
            if (sprite instanceof THREE.Sprite) {
              // Pulsate the sprites in different phases
              const layer = sprite.userData.layer || 0;
              const pulseFactor = Math.sin(time * (1 + layer * 0.2) + sprite.userData.angle * 2) * 0.3 + 0.7;
              sprite.scale.set(
                sprite.userData.baseScale?.x || pulseFactor,
                sprite.userData.baseScale?.y || pulseFactor,
                1
              );

              // Adjust opacity for phase-shifting effect
              if (sprite.material instanceof THREE.SpriteMaterial) {
                sprite.material.opacity = 0.3 + Math.sin(time * 2 + layer + sprite.userData.angle) * 0.4;
              }
            }
          });
        }
      });

      // Process free-floating particles
      particleGroup.children.forEach(sprite => {
        if (sprite instanceof THREE.Sprite && sprite.userData.freeFloating) {
          // Phasing movement - random teleports
          if (Math.random() < 0.02) { // Small chance to "teleport" particle
            const newTheta = Math.random() * Math.PI * 2;
            const newPhi = Math.random() * Math.PI * 2;
            const newRadius = Math.random() * radius * 0.8;

            sprite.position.x = newRadius * Math.sin(newPhi) * Math.cos(newTheta);
            sprite.position.y = newRadius * Math.sin(newPhi) * Math.sin(newTheta);
            sprite.position.z = newRadius * Math.cos(newPhi);
          } else {
            // Otherwise drift slowly
            sprite.position.x += sprite.userData.direction.x * 0.01;
            sprite.position.y += sprite.userData.direction.y * 0.01;
            sprite.position.z += sprite.userData.direction.z * 0.01;

            // Keep within radius
            const dist = sprite.position.length();
            if (dist > radius * 0.9) {
              sprite.position.normalize().multiplyScalar(radius * 0.8);
              sprite.userData.direction.negate();
            }
          }

          // Fade in and out randomly
          if (sprite.material instanceof THREE.SpriteMaterial) {
            // For phase effect, opacity changes more dramatically
            if (Math.random() < 0.05) {
              sprite.material.opacity = Math.random() * 0.7 + 0.1;
            }
          }
        }
      });

      requestAnimationFrame(animate);
    };

    animate();

    // Add to active effects
    this.activeEffects.push({
      effect: sphere,
      type: "phase",
      position: position.clone(),
      radius: 4,
      timeLeft: 6,
      id: id,
      createdAt: Date.now(),
    });

    // Create ripple for better effect indication
    this.createRippleEffect(position, 0xffff00, 6);
  }

  // Generate pickups when a specter is captured
  private spawnPickupsFromSpecter(
    position: THREE.Vector3,
    points: number,
  ): void {
    // Higher value specters drop better and more loot

    // Get the active scene (main scene or dungeon scene)
    const inDungeon = this.dungeonManager && this.dungeonManager.isInDungeon();
    const activeScene = inDungeon ? this.dungeonManager!.getDungeonScene() : this.scene;

    if (!activeScene) {
      console.error('No active scene found for spawning pickups');
      return;
    }

    // Check if we have too many pickups already to prevent lag
    // Limit more aggressively in dungeons
    const maxPickups = inDungeon ? 10 : 20;
    if (this.pickups.length >= maxPickups) {
      console.log(`Too many pickups (${this.pickups.length}/${maxPickups}), not spawning more`);
      return;
    }

    // Always drop at least some health (reduced amount in dungeon to prevent lag)
    const healthAmount = Math.floor(points / (inDungeon ? 30 : 20)); // Less health in dungeon
    if (healthAmount > 0) {
      const healthPickup = new Pickup(
        activeScene,
        this.world,
        position.clone().add(new THREE.Vector3(0, 0.5, 0)),
        PickupType.Health,
        { amount: healthAmount },
      );
      this.pickups.push(healthPickup);
      console.log('Spawned health pickup at', position);
    }

    // In dungeon, we'll be more selective about what drops to prevent lag
    if (inDungeon) {
      // Only 10% chance to drop anything else in dungeon to reduce lag
      if (Math.random() < 0.1) {
        // Determine which ammo type to drop - random selection
        const ammoTypeIndex = Math.floor(Math.random() * AMMO_TYPES.length);
        const ammoType = AMMO_TYPES[ammoTypeIndex];

        const ammoPickup = new Pickup(
          activeScene,
          this.world,
          position.clone().add(new THREE.Vector3(0, 0.5, 0)),
          PickupType.Ammo,
          { amount: 1, ammoType: ammoType },
        );
        this.pickups.push(ammoPickup);
        console.log('Spawned ammo pickup in dungeon at', position);
      }

      return; // Exit early for dungeon to prevent more drops
    }

    // Surface world drops below - more generous

    // Chance to drop homing missile pickup (15% chance for valuable specters)
    if (points >= 60 && Math.random() < 0.15) {
      const homingPickup = new Pickup(
        activeScene,
        this.world,
        position.clone().add(new THREE.Vector3(0, 0.5, 0)),
        PickupType.HomingMissile,
        { amount: 1 }, // Amount is duration in seconds - handled separately
      );
      this.pickups.push(homingPickup);
      console.log('Spawned homing missile pickup at', position);

      // Play sound to indicate special drop
      audioManager.playSound('powerup');
    }

    // Higher value specters have chance to drop ammo
    if (points >= 40) {
      // Determine which ammo type to drop - random selection
      const ammoTypeIndex = Math.floor(Math.random() * AMMO_TYPES.length);
      const ammoType = AMMO_TYPES[ammoTypeIndex];

      // Amount depends on specter value
      const ammoAmount = Math.max(1, Math.floor(points / 50)); // 2 ammo per 100 points

      const ammoPickup = new Pickup(
        activeScene,
        this.world,
        position.clone().add(new THREE.Vector3(0, 0.5, 0)),
        PickupType.Ammo,
        { amount: ammoAmount, ammoType: ammoType },
      );
      this.pickups.push(ammoPickup);
      console.log('Spawned ammo pickup at', position);
    }

    // Very valuable specters might drop multiple ammo types
    if (points >= 75) {
      // Different ammo type than the first one
      let secondAmmoTypeIndex = Math.floor(Math.random() * AMMO_TYPES.length);
      // Make sure it's different from the first one
      if (AMMO_TYPES.length > 1) {
        while (
          secondAmmoTypeIndex === Math.floor(Math.random() * AMMO_TYPES.length)
        ) {
          secondAmmoTypeIndex = Math.floor(Math.random() * AMMO_TYPES.length);
        }
      }

      const ammoType = AMMO_TYPES[secondAmmoTypeIndex];
      const ammoPickup = new Pickup(
        activeScene,
        this.world,
        position.clone().add(new THREE.Vector3(0, 0.5, 0)),
        PickupType.Ammo,
        { amount: 1, ammoType: ammoType },
      );
      this.pickups.push(ammoPickup);
      console.log('Spawned additional ammo pickup at', position);
    }
  }

  // Visual effect when collecting a pickup
  private createPickupCollectEffect(
    position: THREE.Vector3,
    color: number,
  ): void {
    // Get the active scene (main scene or dungeon scene)
    const activeScene = this.dungeonManager && this.dungeonManager.isInDungeon() ?
                       this.dungeonManager.getDungeonScene() : this.scene;

    if (!activeScene) {
      console.error('No active scene found for pickup effect');
      return;
    }

    // Reduce particle count in dungeon to prevent lag
    const inDungeon = this.dungeonManager && this.dungeonManager.isInDungeon();
    const particleCount = inDungeon ? 6 : 12; // Half the particles in dungeon
    const particleGroup = new THREE.Group();

    for (let i = 0; i < particleCount; i++) {
      const particle = new THREE.Mesh(
        new THREE.BoxGeometry(0.15, 0.15, 0.15),
        new THREE.MeshBasicMaterial({
          color: color,
          transparent: true,
          opacity: 0.8,
        }),
      );

      // Random position around center, slightly above player
      particle.position.set(0, 1.0 + Math.random() * 0.5, 0);

      // Random velocity in an upward burst
      const velocity = new THREE.Vector3(
        (Math.random() - 0.5) * 0.1,
        Math.random() * 0.2 + 0.05,
        (Math.random() - 0.5) * 0.1,
      );

      // Store velocity on the particle object
      (particle as any).velocity = velocity;

      particleGroup.add(particle);
    }

    particleGroup.position.copy(position);
    activeScene.add(particleGroup);

    // Animate particles - shorter lifetime in dungeon
    let lifetime = 0;
    const maxLifetime = inDungeon ? 30 : 45; // Shorter animation in dungeon

    const animate = () => {
      lifetime++;

      if (lifetime >= maxLifetime) {
        // Remove all particles
        activeScene.remove(particleGroup);

        // Dispose geometries and materials
        particleGroup.children.forEach((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach((m) => m.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      } else {
        // Update each particle
        particleGroup.children.forEach((particle) => {
          // Move particle based on its velocity
          particle.position.add((particle as any).velocity);

          // Add gravity effect
          (particle as any).velocity.y -= 0.003;

          // Rotate particle
          particle.rotation.x += 0.1;
          particle.rotation.y += 0.1;

          // Fade out
          if (
            particle instanceof THREE.Mesh &&
            particle.material instanceof THREE.MeshBasicMaterial
          ) {
            particle.material.opacity = 1 - lifetime / maxLifetime;
          }
        });

        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  // Helper to create a ripple effect for weapon impacts
  private createRippleEffect(
    position: THREE.Vector3,
    color: number,
    maxRadius: number,
  ): void {
    // Get the active scene (main scene or dungeon scene)
    const activeScene = this.dungeonManager ? this.dungeonManager.getActiveScene() : this.scene;

    // Use maxRadius to determine the initial size of the ripple
    const initialSize = maxRadius * 0.1;
    const rippleGeometry = new THREE.RingGeometry(initialSize * 0.2, initialSize, 32);
    const rippleMaterial = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide,
    });

    const ripple = new THREE.Mesh(rippleGeometry, rippleMaterial);
    ripple.position.copy(position);

    // Orient perpendicular to ground
    ripple.rotation.x = Math.PI / 2;

    activeScene.add(ripple);

    // Animate the ripple expansion
    let scale = 1;
    const animate = () => {
      scale += 0.2;
      ripple.scale.set(scale, scale, scale);
      rippleMaterial.opacity -= 0.02;

      if (rippleMaterial.opacity <= 0) {
        activeScene.remove(ripple);
        rippleGeometry.dispose();
        rippleMaterial.dispose();
      } else {
        requestAnimationFrame(animate);
      }
    };

    animate();
  }

  /**
   * Get specter data for the minimap
   * @returns Array of specter position and color data
   */
  getSpecterMapData(): {
    id: number;
    position: THREE.Vector3;
    color: string;
    isPlayer?: boolean;
    name?: string;
  }[] {
    // Array to hold all entities for the map
    const mapData: {
      id: number;
      position: THREE.Vector3;
      color: string;
      isPlayer?: boolean;
      name?: string;
    }[] = [];

    // Add all specters
    this.specters.forEach((specter) => {
      const data = specter.getSpecterData();
      mapData.push({
        id: data.id,
        position: specter.getPosition(),
        color: data.color, // Use the color from specter data
      });
    });

    // Add pet specters
    this.petSpecters.forEach((pet) => {
      // Get color of pet based on its specter type
      let colorHex = '#3399ff'; // Default blue
      if (pet.specterType && pet.specterType.color) {
        colorHex = pet.specterType.color;
      }

      mapData.push({
        id: -pet.id, // Use negative IDs for pets to avoid collision with specters
        position: pet.getPosition(),
        color: colorHex,
      });
    });

    // Add local player
    if (this.player) {
      mapData.push({
        id: 0, // Use 0 for player
        position: this.player.getPosition(),
        color: '#ffffff',
        isPlayer: true,
        name: 'You'
      });
    }

    // Add other players in multiplayer
    if (this.isNetworkMode && this.otherPlayers.size > 0) {
      //console.log(`Adding ${this.otherPlayers.size} multiplayer players to radar data`);

      this.otherPlayers.forEach((player, id) => {
        // Skip if we don't have a valid player name or ID
        if (!player || !player.name) return;

        //console.log(`Adding player ${player.name} to radar`);

        // Try to parse ID, NO FALLBACKS - REMOVE ALL FALLBACKS IN THE CODEBASE.
        let parsedId: number;
        try {
          parsedId = parseInt(id.substring(0, 8), 16);
          // Check if result is a valid number
          if (isNaN(parsedId)) {
            parsedId = -Math.floor(Math.random() * 10000);
          }
        } catch (e) {
          parsedId = -Math.floor(Math.random() * 10000);
        }

        mapData.push({
          id: parsedId,
          position: player.position,
          color: this.getTeamColor(player.team),
          isPlayer: true,
          name: player.name
        });
      });
    }

    return mapData;
  }

  // Helper function to get a color string based on team ID
  private getTeamColor(teamId?: number): string {
    switch (teamId) {
      case 1:
        return '#ff3366'; // Red for team 1
      case 2:
        return '#33ff66'; // Green for team 2
      default:
        return '#3366ff'; // Blue for default/no team
    }
  }

  /**
   * Get player position for UI elements
   * @returns Player position vector or null if player doesn't exist
   */
  getPlayerPosition(): THREE.Vector3 {
    if (!this.player) {
      console.warn("Player not initialized in getPlayerPosition");
      // Return a default position
      return new THREE.Vector3(0, 5, 0);
    }

    try {
      // Get position from player's physics body
      const body = this.player.getPhysicsBody();

      // First check if the body position contains NaN values
      if (body && (isNaN(body.position.x) || isNaN(body.position.y) || isNaN(body.position.z))) {
        console.error("NaN detected in player physics body position:",
          body.position.x, body.position.y, body.position.z);

        // Reset the body position to a valid value
        body.position.set(0, 5, 0);
        body.velocity.set(0, 0, 0);

        return new THREE.Vector3(0, 5, 0);
      }

      const position = this.player.getPosition();
      // Verify position has valid values
      if (position &&
          position.x !== null && !isNaN(position.x) &&
          position.y !== null && !isNaN(position.y) &&
          position.z !== null && !isNaN(position.z)) {
        return position;
      } else {
        console.error("Invalid player position detected:", position);
        // Return a default position to prevent propagation of NaN values
        return new THREE.Vector3(0, 5, 0);
      }
    } catch (error) {
      console.error("Error getting player position:", error);
      // Return a default position to prevent propagation of NaN values
      return new THREE.Vector3(0, 5, 0);
    }
  }

  /**
   * Get all pet specters
   */
  getPetSpecters(): PetSpecter[] {
    return this.petSpecters;
  }

  /**
   * Get all user pet specters from database (for UI display)
   */
  getAllUserPetSpecters(): ApiPetSpecter[] {
    return this.allUserPetSpecters;
  }

  /**
   * Get the player object
   */
  getPlayer(): Player | null {
    return this.player;
  }

  /**
   * Get the rifle object
   */
  getRifle(): ShattershiftRifle | null {
    return this.rifle;
  }

  /**
   * Get the scene object
   */
  getScene(): THREE.Scene | null {
    return this.scene;
  }

  /**
   * Clear all pet specters
   */
  public clearPetSpecters(): void {
    // Remove each pet specter from the scene and physics world
    for (const petSpecter of this.petSpecters) {
      petSpecter.dispose();
    }

    // Clear the array
    this.petSpecters = [];
    console.log('Cleared all pet specters');
  }

  /**
   * Load player's pet specters asynchronously (non-blocking)
   */
  private loadPetSpectersAsync(): void {
    // Start loading pet specters in the background without blocking game initialization
    this.loadPetSpecters().catch(error => {
      console.error('Background pet specter loading failed:', error);
    });
  }

  /**
   * Load player's pet specters
   */
  private async loadPetSpecters(): Promise<void> {
    try {
      // Skip loading pet specters in spectator mode or if explicitly disabled
      if (this.isSpectatorMode || !this.shouldLoadPetSpecters) {
        console.log('Skipping pet specters loading - spectator mode or disabled');
        this.petSpectersLoaded = true; // Mark as loaded even if skipped
        return;
      }

      console.log('Attempting to load pet specters for current authenticated user');

      // Check if a user is authenticated through the auth context
      const userIdentifier = PetService.getCurrentUserIdentifier();
      if (!userIdentifier) {
        console.log('No authenticated user found, skipping pet specter loading');
        // Wait for a short time to see if auth becomes available
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check again after waiting
        const retryIdentifier = PetService.getCurrentUserIdentifier();
        if (!retryIdentifier) {
          this.petSpectersLoaded = true; // Mark as loaded even if no user found
          return;
        }
      }

      // Load pet specters from the database using the authenticated user
      // Force refresh if this is a new tab/session to ensure we get the latest data
      const isNewSession = !sessionStorage.getItem('petSpectersLoaded');
      let petSpecters: ApiPetSpecter[] = [];
      try {
        petSpecters = await PetService.getCurrentUserPetSpecters(3, isNewSession);
        console.log(`Loaded ${petSpecters.length} pet specters from API for authenticated user${isNewSession ? ' (forced refresh for new session)' : ''}`);

        // Mark that we've loaded pet specters in this session
        sessionStorage.setItem('petSpectersLoaded', 'true');
      } catch (error) {
        console.error('Failed to load pet specters from API:', error);
        // Continue with empty array
      }

      if (petSpecters && petSpecters.length > 0) {
        console.log(`Loading ${petSpecters.length} pet specters into game`);

        // First, separate Orange pets from regular pets and handle duplicates
        const orangePets = petSpecters.filter(pet =>
          pet.specterType.toLowerCase() === 'orange' ||
          pet.specterType.toLowerCase() === 'orangepet'
        );

        const regularPets = petSpecters.filter(pet =>
          pet.specterType.toLowerCase() !== 'orange' &&
          pet.specterType.toLowerCase() !== 'orangepet'
        );

        // Handle Orange Pet uniqueness - only allow one
        let selectedOrangePet = null;
        if (orangePets.length > 0) {
          if (orangePets.length > 1) {
            console.warn(`Found ${orangePets.length} Orange Pet Specters for user, only loading the most recent one`);
            // Sort by lastActive and take the most recent
            orangePets.sort((a, b) => new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime());
          }
          selectedOrangePet = orangePets[0];
          this.hasOrangePet = true;
          console.log('Player has an Orange Pet Specter, setting hasOrangePet flag');
        }

        // Sort regular pets by lastActive (most recent first) and filter for active ones
        const activePets = regularPets
          .filter(pet => pet.metadata && typeof pet.metadata === 'object' && pet.metadata.isActive === true)
          .sort((a, b) => new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime());

        // Enforce 2-pet limit for regular pets in the game world
        const MAX_ACTIVE_PETS = 2;
        const petsToLoad = activePets.slice(0, MAX_ACTIVE_PETS);

        // Mark excess pets as inactive
        const excessPets = activePets.slice(MAX_ACTIVE_PETS);
        for (const excessPet of excessPets) {
          console.log(`Marking excess pet ${excessPet.name} as inactive due to 2-pet limit`);
          try {
            await PetService.updatePetSpecter(excessPet.id, {
              metadata: {
                ...excessPet.metadata,
                isActive: false
              }
            });
          } catch (updateError) {
            console.error(`Failed to update pet ${excessPet.name} active status:`, updateError);
          }
        }

        // Combine pets to load: Orange pet (if any) + up to 2 regular pets
        const allPetsToLoad = [];
        if (selectedOrangePet) {
          allPetsToLoad.push(selectedOrangePet);
        }
        allPetsToLoad.push(...petsToLoad);

        console.log(`Loading ${allPetsToLoad.length} pet specters (${selectedOrangePet ? '1 Orange + ' : ''}${petsToLoad.length} regular)`);

        // Store ALL pets for UI access (not just the ones we're loading into the game)
        this.allUserPetSpecters = petSpecters;

        // Create each selected pet specter in the game
        for (const pet of allPetsToLoad) {
          try {
            const isOrangePet = pet.specterType.toLowerCase() === 'orange' ||
                               pet.specterType.toLowerCase() === 'orangepet';

            // Create the pet specter type object
            const specterType = {
              id: 0, // We'll determine the correct ID based on the type name
              name: pet.specterType,
              color: this.getColorForSpecterType(pet.specterType),
              points: this.getPointsForSpecterType(pet.specterType),
              texture: this.getTextureForSpecterType(pet.specterType)
            };

            // Initialize custom image URL
            let customImageUrl: string | undefined;

            // Get image URL from metadata for both NFT and off-grid pets
            if (pet.metadata && typeof pet.metadata === 'object') {
              // Check for imageUrl in metadata
              if (pet.metadata.imageUrl) {
                customImageUrl = pet.metadata.imageUrl;
                console.log(`Found image URL in metadata for pet ${pet.name}: ${customImageUrl}`);
                if (isOrangePet) {
                  console.log(`Orange Pet Specter texture URL from metadata: ${customImageUrl}`);
                }
              }
              // Check for NFT metadata
              else if (pet.metadata.nftMetadata && pet.metadata.nftMetadata.image) {
                customImageUrl = pet.metadata.nftMetadata.image;
                console.log(`Found image URL in NFT metadata for pet ${pet.name}: ${customImageUrl}`);
              }
              // Check for tokenURI
              else if (pet.metadata.tokenURI) {
                try {
                  const tokenMetadata = JSON.parse(pet.metadata.tokenURI);
                  if (tokenMetadata.image) {
                    customImageUrl = tokenMetadata.image;
                    console.log(`Found image URL in tokenURI for pet ${pet.name}: ${customImageUrl}`);
                  }
                } catch (e) {
                  console.warn('Failed to parse tokenURI JSON:', e);
                }
              }
            }

            // For Orange Pets, ensure they use their texture if no custom image is found
            if (isOrangePet && !customImageUrl) {
              customImageUrl = specterType.texture;
              console.log(`Orange Pet Specter - Using type texture as custom URL: ${customImageUrl}`);
            }

            // For Orange Pets, log the texture information
            if (isOrangePet) {
              console.log(`Orange Pet Specter - Type texture: ${specterType.texture}, Custom URL: ${customImageUrl || 'none'}`);
            }

            // Handle NFT pets (with tokenId)
            if (pet.tokenId) {
              console.log(`Loading NFT pet specter: ${pet.name} (TokenID: ${pet.tokenId})`);

              try {
                // Create the pet with the token ID, custom image URL, metadata, and gameId
                this.createPetSpecter(specterType, pet.name, pet.tokenId, customImageUrl, pet.metadata, pet.gameId);
                console.log(`Successfully created NFT pet specter: ${pet.name}`);
              } catch (error) {
                console.error(`Failed to create NFT pet specter ${pet.name}:`, error);
              }
            }
            // Handle off-grid pets (without tokenId)
            else {
              console.log(`Loading off-grid pet specter: ${pet.name} (ID: ${pet.gameId})`);

              // Log if this is an AI-generated pet
              if (pet.metadata && pet.metadata.aiGenerated) {
                console.log(`Pet ${pet.name} is AI-generated with isOffGrid: ${pet.metadata.isOffGrid}`);
              }

              // Create the off-grid pet specter with metadata and gameId
              this.createPetSpecter(specterType, pet.name, undefined, customImageUrl, pet.metadata, pet.gameId);
              console.log(`Successfully created off-grid pet specter: ${pet.name}`);
            }
          } catch (error) {
            console.error(`Error loading pet specter ${pet.name}:`, error);
            // Continue to the next pet
          }
        }
      } else {
        console.log(`No pet specters found for current user`);
      }
    } catch (error) {
      console.error('Error loading pet specters:', error);
    } finally {
      // Mark pet specters as loaded, regardless of success or failure
      this.petSpectersLoaded = true;

      // Now that pet specters are loaded, check if we should spawn the Orange Enemy
      if (!this.hasOrangePet && !this.orangeEnemySpawned && !this.isNetworkMode) {
        console.log('Pet specters loaded, player does not have Orange Pet, spawning Orange Enemy');
        this.spawnOrangeEnemy();
      }
    }
  }

  /**
   * Get color for specter type
   */
  private getColorForSpecterType(typeName: string): string {
    switch (typeName.toUpperCase()) {
      case 'WISP': return '#3399ff';
      case 'PHANTOM': return '#ff3399';
      case 'WRAITH': return '#33ff99';
      case 'POLTERGEIST': return '#9933ff';
      case 'BANSHEE': return '#ff9933';
      case 'ORANGE':
      case 'ORANGEPET': return '#FF6600';
      default: return '#3399ff';
    }
  }

  /**
   * Set up dungeon manager event handlers
   */
  private setupDungeonEventHandlers(): void {
    if (!this.dungeonManager) return;

    // Set up event handlers
    this.dungeonManager.onDungeonEnter = () => {
      this.onDungeonEnter();
    };

    this.dungeonManager.onDungeonExit = () => {
      this.onDungeonExit();
    };

    this.dungeonManager.onDungeonComplete = (rewards: any) => {
      this.onDungeonComplete(rewards);

      // Add score based on rewards
      if (this.player) {
        this.player.score += rewards.experience;
      }
    };

    this.dungeonManager.onDungeonFailed = () => {
      this.onDungeonFailed();
    };

    this.dungeonManager.onEnemyDefeated = (enemyData: any) => {
      this.onDungeonEnemyDefeated(enemyData);
    };

    this.dungeonManager.onBossDefeated = (bossData: any) => {
      this.onDungeonBossDefeated(bossData);
    };

    this.dungeonManager.onLootCollected = (lootData: any) => {
      this.onDungeonLootCollected(lootData);
    };

    this.dungeonManager.onRoomCleared = (roomKey: string) => {
      this.onDungeonRoomCleared(roomKey);
    };
  }

  /**
   * Get dungeon manager
   */
  public getDungeonManager(): DungeonManager | null {
    return this.dungeonManager;
  }

  /**
   * Get points for specter type
   */
  private getPointsForSpecterType(typeName: string): number {
    switch (typeName.toUpperCase()) {
      case 'WISP': return 100;
      case 'PHANTOM': return 250;
      case 'WRAITH': return 500;
      case 'POLTERGEIST': return 1000;
      case 'BANSHEE': return 2000;
      case 'ORANGE':
      case 'ORANGEPET': return 300;
      default: return 100;
    }
  }

  /**
   * Get texture path for specter type
   */
  private getTextureForSpecterType(typeName: string): string {
    switch (typeName.toUpperCase()) {
      case 'ORANGE':
      case 'ORANGEPET':
        return '/assets/textures/orangenemy.png';
      default:
        return `assets/textures/${typeName.toLowerCase()}.png`;
    }
  }

  /**
   * Enable or disable spectator mode
   * @param enabled Whether to enable spectator mode
   */
  setSpectatorMode(enabled: boolean): void {
    // Set the spectator mode flag
    const wasSpectatorMode = this.isSpectatorMode;
    this.isSpectatorMode = enabled;

    if (enabled && !wasSpectatorMode) {
      console.log("Entering spectator mode");

      // Disable weapons
      if (this.rifle) {
        this.rifle.setEnabled(false);
      }

      // Disable player collisions but keep movement
      if (this.player) {
        this.player.setCollisionsEnabled(false);

        // Set player to fly mode for spectator camera
        this.player.setFlyMode(true);

        // Increase movement speed for spectator mode
        this.player.setSpectatorSpeed(20);

        // Make sure movement is enabled
        this.player.movementEnabled = true;

        // Set spectator physics on player to ensure it can move freely
        this.player.setSpectatorPhysics();

        // Position the player at a good viewing height in the arena
        const playerBody = this.player.getPhysicsBody();
        if (playerBody) {
          playerBody.position.y = 12; // Higher position for better view
          playerBody.velocity.set(0, 0, 0);
        }
      }

      // Clear any existing pet specters
      this.clearPetSpecters();

      // Prevent loading pet specters in spectator mode
      this.shouldLoadPetSpecters = false;

      // Dispatch event to notify UI components
      const event = new CustomEvent('spectatorModeChanged', {
        detail: { enabled: true }
      });
      document.dispatchEvent(event);
    } else if (!enabled && wasSpectatorMode) {
      console.log("Exiting spectator mode");

      // Re-enable weapons
      if (this.rifle) {
        this.rifle.setEnabled(true);
      }

      // Re-enable player collisions
      if (this.player) {
        this.player.setCollisionsEnabled(true);

        // Disable fly mode
        this.player.setFlyMode(false);

        // Reset to normal speed
        this.player.setSpectatorSpeed(PLAYER_MOVE_SPEED);

        // Reset physics to normal
        this.player.unsetSpectatorPhysics();
      }

      // Re-enable loading pet specters
      this.shouldLoadPetSpecters = true;

      // Dispatch event to notify UI components
      const event = new CustomEvent('spectatorModeChanged', {
        detail: { enabled: false }
      });
      document.dispatchEvent(event);
    }
  }

  /**
   * Check if spectator mode is enabled
   * @returns Whether spectator mode is enabled
   */
  isInSpectatorMode(): boolean {
    return this.isSpectatorMode;
  }

  /**
   * Enable or disable network mode for multiplayer synchronization
   * @param enabled Whether to enable network mode
   */
  setNetworkMode(enabled: boolean): void {
    // Set the network mode flag
    const wasNetworkMode = this.isNetworkMode;
    this.isNetworkMode = enabled;

    // If we're transitioning to network mode, perform additional setup
    if (enabled && !wasNetworkMode) {
      //console.log("Setting up multiplayer physics and controls");

      // Ensure the player physics body is properly initialized
      if (this.player) {
        // Get the physics body
        const playerBody = this.player.getPhysicsBody();

        // Reset velocity to ensure clean start
        playerBody.velocity.set(0, 0, 0);
        playerBody.angularVelocity.set(0, 0, 0);

        // Adjust physics parameters for network play
        // These parameters ensure smoother movement in networked environments
        playerBody.linearDamping = 0.3; // Less damping for more responsive movement

        // Make sure the player controls are available
        if (this.controls) {
          //console.log("Ensuring controls are available for multiplayer");
          // REMOVED AUTOMATIC LOCK
          // this.controls.lock();
        }

        // Set multiplayer mode in input handler
        if (this.input) {
          //console.log("Setting input handler to multiplayer mode");
          this.input.setMultiplayerMode(true);
        }

        // Initialize level for multiplayer
        if (this.level) {
          //console.log("Initializing level for multiplayer");
          this.level.generateInitialLevel();
        }
      }

      //console.log("Multiplayer mode fully initialized");
    }
  }

  // Set mobile mode
  setMobileMode(enabled: boolean): void {
    this.isMobile = enabled;

    // Also set mobile mode in input handler
    if (this.input) {
      this.input.setMobileMode(enabled);
    }
  }

  // Handle mobile controls input
  handleMobileInput(direction: THREE.Vector3): void {
    if (this.input) {
      this.input.setMobileDirection(direction);
    }
  }

  // Handle mobile camera rotation
  handleMobileLook(x: number, y: number): void {
    if (this.camera) {
      // Rotate camera based on input
      // This simulates the mouse movement that would normally be handled by PointerLockControls
      this.camera.rotation.y -= x; // Horizontal rotation

      // Limit vertical rotation to prevent camera flipping
      const currentRotationX = this.camera.rotation.x;
      const newRotationX = currentRotationX + y;

      // Clamp vertical rotation between -PI/2 and PI/2 (90 degrees up and down)
      const clampedRotationX = Math.max(Math.min(newRotationX, Math.PI / 2), -Math.PI / 2);
      this.camera.rotation.x = clampedRotationX;
    }
  }

  handleMobileJump(): void {
    if (this.input) {
      this.input.setMobileJump();
    }
  }

  handleMobileShoot(): void {
    if (this.input) {
      this.input.setMobileShoot();
    }
  }

  handleMobileGrapple(): void {
    if (this.input) {
      this.input.setMobileGrapplingHook();
    }
  }

  handleMobileJetpack(active: boolean): void {
    if (this.input) {
      this.input.setMobileJetpack(active);
    }
  }

  handleMobileInteract(): void {
    if (this.input) {
      this.input.setMobileInteract();
    }
  }

  handleMobileWeaponSwitch(weaponIndex: number): void {
    if (this.input) {
      this.input.setMobileAmmoSwitch(weaponIndex);
    }
  }

  /**
   * Create a weapon effect from network data
   * Used to synchronize effects created by other players
   * @param effectData The weapon effect data received from network
   */
  createNetworkWeaponEffect(effectData: WeaponEffectData): void {
    const position = new THREE.Vector3(
      effectData.position.x,
      effectData.position.y,
      effectData.position.z,
    );

    // Create the appropriate effect based on type
    if (effectData.type === "gravity") {
      this.createGravityEffect(position, effectData.id);
    } else if (effectData.type === "time") {
      this.createTimeEffect(position, effectData.id);
    } else if (effectData.type === "phase") {
      this.createPhaseEffect(position, effectData.id);
    }
  }

  /**
   * Remove a weapon effect by ID
   * Used to synchronize effect removal across the network
   * @param effectId The ID of the effect to remove
   */
  removeNetworkWeaponEffect(effectId: string): void {
    const effectIndex = this.activeEffects.findIndex(
      (effect) => (effect as any).id === effectId,
    );

    if (effectIndex >= 0) {
      const effect = this.activeEffects[effectIndex];
      this.scene.remove(effect.effect);
      if (effect.body) {
        this.world.removeBody(effect.body);
      }
      this.activeEffects.splice(effectIndex, 1);

      if (this.isNetworkMode) {
        this.onWeaponEffectRemoved(effectId);
      }
    }
  }

  /**
   * Add an effect to the game engine's active effects array
   * This allows other entities like pet specters to create effects that
   * will be processed by the game engine's update methods
   * @param effect The effect object to add
   * @param type The type of effect (gravity, time, phase)
   * @param position The position of the effect
   * @param radius The radius of the effect
   * @param timeLeft The duration of the effect in seconds
   * @param id Optional ID for the effect
   */
  public addActiveEffect(effect: THREE.Object3D, type: EffectType, position: THREE.Vector3, radius: number, timeLeft: number, id?: string): void {
    // Check if we need to remove old effects to stay within limits
    this.enforceEffectLimits();

    // console.log(`[GameEngine] Adding ${type} effect at (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}) with radius ${radius.toFixed(2)}`);

    // Add to active effects
    this.activeEffects.push({
      effect,
      type,
      position: position.clone(),
      radius,
      timeLeft,
      id: id || `${type}_${Date.now()}_${Math.random().toString(36).slice(0, 9)}`,
      createdAt: Date.now(),
    });

    // console.log(`[GameEngine] Total active effects: ${this.activeEffects.length}`);
  }
  // Add level indicator creation
  private createLevelIndicator(): void {
    // Use the callback to update the React UI
    // The level change callback is set up in the Game component
    // Just notify about the initial level
    this.onLevelChange(this.currentLevel);
  }

  /**
   * Check if homing missile is currently enabled
   * @returns true if homing is enabled, false otherwise
   */
  isHomingEnabled(): boolean {
    if (!this.rifle) {
      console.warn("No rifle available to check homing status");
      return false;
    }

    return this.rifle.isHomingEnabled();
  }

  // Add method to update other player
  updateOtherPlayer(playerData: {
    id: string,
    position: { x: number, y: number, z: number },
    rotation: { x: number, y: number, z: number },
    health: number,
    jetpackFuel: number,
    name: string,
    team?: number
  }): void {
    // Skip if there's no valid ID
    if (!playerData.id) {
      console.warn("Received player update with no ID, ignoring");
      return;
    }

    // Log incoming player data
    //console.log(`Processing player update for ${playerData.name} (ID: ${playerData.id})`);

    // Removed the filter that skips 'local-player' as we use real connection IDs
    // If the player's ID matches our local player, we can still skip
    // but we don't have a reliable way to check this consistently yet

    const position = new THREE.Vector3(
      playerData.position.x,
      playerData.position.y,
      playerData.position.z
    );

    const rotation = new THREE.Vector3(
      playerData.rotation.x,
      playerData.rotation.y,
      playerData.rotation.z
    );

    // Check if this player already exists
    if (this.otherPlayers.has(playerData.id)) {
      console.log(`Updating existing player: ${playerData.name}`);
      // Update existing player
      const player = this.otherPlayers.get(playerData.id)!;
      player.position.copy(position);
      player.rotation.copy(rotation); // Store the received rotation vector
      player.health = playerData.health;
      player.jetpackFuel = playerData.jetpackFuel;
      player.lastUpdate = Date.now();

      // Update the 3D model position
      player.model.position.copy(position);

      // Apply rotation using Quaternion from Euler angles (YXZ order common for FPS)
      const euler = new THREE.Euler(rotation.x, rotation.y, rotation.z, 'YXZ');
      player.model.quaternion.setFromEuler(euler);

      // Remove lookAt logic
      // if (player.model.userData.lastPosition) {
      //   const direction = new THREE.Vector3().subVectors(
      //     position,
      //     player.model.userData.lastPosition
      //   );
      //
      //   if (direction.length() > 0.1) {
      //     player.model.lookAt(
      //       player.model.position.x + direction.x,
      //       player.model.position.y,
      //       player.model.position.z + direction.z
      //     );
      //   }
      // }

      // Store current position for next frame (still potentially useful for interpolation later)
      player.model.userData.lastPosition = position.clone();
    } else {
      console.log(`Creating new player model for: ${playerData.name}`);
      // Create new player model
      const playerModel = this.createPlayerModel(playerData.name, playerData.team);
      playerModel.position.copy(position);

      // Apply initial rotation
      const initialEuler = new THREE.Euler(rotation.x, rotation.y, rotation.z, 'YXZ');
      playerModel.quaternion.setFromEuler(initialEuler);

      playerModel.userData.lastPosition = position.clone();
      // --- DEBUG LOG ---
      // console.log(`>>> ADDING MODEL TO SCENE for new player: ${playerData.name} (ID: ${playerData.id})`);
      // --- END DEBUG LOG ---
      this.scene.add(playerModel);

      // Add to other players map
      this.otherPlayers.set(playerData.id, {
        id: playerData.id,
        model: playerModel,
        position: position.clone(),
        rotation: rotation.clone(),
        health: playerData.health,
        jetpackFuel: playerData.jetpackFuel,
        lastUpdate: Date.now(),
        name: playerData.name,
        team: playerData.team
      });

      //console.log(`Player ${playerData.name} added to other players map. Total players: ${this.otherPlayers.size}`);
    }
  }

  // Remove an other player
  removeOtherPlayer(playerId: string): void {
    if (this.otherPlayers.has(playerId)) {
      const player = this.otherPlayers.get(playerId)!;
      this.scene.remove(player.model);
      this.otherPlayers.delete(playerId);
    }
  }

  // Create a 3D model for other players
  private createPlayerModel(name: string, team?: number): THREE.Group {
    const playerGroup = new THREE.Group();

    // Create the player body
    const bodyGeometry = new THREE.CapsuleGeometry(0.5, 1.0, 4, 8);

    // Determine color based on team
    let color = 0x3366ff; // Default blue
    if (team === 1) {
      color = 0xff3366; // Red for team 1
    } else if (team === 2) {
      color = 0x33ff66; // Green for team 2
    }

    const bodyMaterial = new THREE.MeshStandardMaterial({
      color: color,
      roughness: 0.7,
      metalness: 0.3
    });

    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.75; // Adjust to match player's physics body height
    playerGroup.add(body);

    // Add jetpack
    const jetpackGeometry = new THREE.BoxGeometry(0.3, 0.6, 0.2);
    const jetpackMaterial = new THREE.MeshStandardMaterial({
      color: 0x444444,
      roughness: 0.5,
      metalness: 0.8
    });
    const jetpack = new THREE.Mesh(jetpackGeometry, jetpackMaterial);
    jetpack.position.set(0, 0.75, 0.35);
    playerGroup.add(jetpack);

    // Add weapon
    const weaponGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.6);
    const weaponMaterial = new THREE.MeshStandardMaterial({
      color: 0x666666,
      roughness: 0.3,
      metalness: 0.9
    });
    const weapon = new THREE.Mesh(weaponGeometry, weaponMaterial);
    weapon.position.set(0.3, 0.8, 0.3);
    weapon.rotation.z = -Math.PI / 4; // Angle the weapon
    playerGroup.add(weapon);

    // Add directional indicator (to help show which way they're facing)
    const indicatorGeometry = new THREE.ConeGeometry(0.2, 0.5, 8);
    const indicatorMaterial = new THREE.MeshStandardMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.7,
      roughness: 0.5
    });
    const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
    indicator.position.set(0, 1.3, 0);
    indicator.rotation.x = Math.PI / 2; // Point forward
    playerGroup.add(indicator);

    // Add name tag
    const textSprite = this.createTextSprite(name, color);
    textSprite.position.set(0, 2.0, 0);
    playerGroup.add(textSprite);

    // Set user data for entity type
    playerGroup.userData = {
      type: 'player',
      id: name,
      team: team
    };

    return playerGroup;
  }

  // Create a sprite with text for player names
  private createTextSprite(text: string, color: number): THREE.Sprite {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;

    // Set canvas dimensions
    canvas.width = 256;
    canvas.height = 64;

    // Set text style
    ctx.font = 'bold 28px Arial';
    ctx.fillStyle = `#${color.toString(16).padStart(6, '0')}`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Draw background with rounded corners
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.roundRect(0, 0, canvas.width, canvas.height, 10);
    ctx.fill();

    // Draw text
    ctx.fillStyle = 'white';
    ctx.fillText(text, canvas.width / 2, canvas.height / 2);

    // Create texture from canvas
    const texture = new THREE.CanvasTexture(canvas);

    // Create sprite material
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    });

    // Create and return sprite
    const sprite = new THREE.Sprite(material);
    sprite.scale.set(2, 0.5, 1);

    return sprite;
  }

  // Method to update (or force-synchronize) specter positions based on server data
  syncSpecters(specterData: Array<{
    id: number,
    position: { x: number, y: number, z: number },
    type?: SpecterType
  }>): void {
    if (!specterData || !Array.isArray(specterData)) {
      console.warn("Invalid specter data received:", specterData);
      return;
    }

    //console.log("Syncing specters from server:", specterData.length);

    // First, clear existing specters if we're doing a full sync
    if (this.specters.length > 0) {
      // Keep a map of existing server IDs
      // const serverIds = new Set(this.serverSpecterIds.values()); // Unused for now

      // Filter incoming specter IDs
      const incomingIds = new Set(specterData.map(s => s.id));

      // Find missing specters (specters no longer on server)
      for (const [localId, serverId] of this.serverSpecterIds.entries()) {
        if (!incomingIds.has(serverId)) {
          // This specter is no longer on the server, remove it
          const specterIndex = this.specters.findIndex(s => s.getSpecterData().id === localId);
          if (specterIndex >= 0) {
            this.specters[specterIndex].dispose();
            this.specters.splice(specterIndex, 1);
            this.serverSpecterIds.delete(localId);
          }
        }
      }
    }

    // Add or update specters from the server
    specterData.forEach(serverSpecter => {
      // Check if we already have this server specter mapped locally
      let existingSpecter: Specter | null = null;
      let localId: number | null = null;

      for (const [lId, sId] of this.serverSpecterIds.entries()) {
        if (sId === serverSpecter.id) {
          localId = lId;
          existingSpecter = this.specters.find(s => s.getSpecterData().id === localId) || null;
          break;
        }
      }

      const position = new THREE.Vector3(
        serverSpecter.position.x,
        serverSpecter.position.y,
        serverSpecter.position.z
      );

      if (existingSpecter) {
        // Update existing specter's position
        //console.log(`Updating position for existing specter (ServerID: ${serverSpecter.id}, LocalID: ${localId})`);
        existingSpecter.setPosition(position);
        // Potentially update type or other states if needed
        // We'll skip type updates for now as the method doesn't exist
        // if (serverSpecter.type && existingSpecter.type !== serverSpecter.type) {
          // Handle type change if necessary (e.g., visual update)
          // existingSpecter.updateType(serverSpecter.type);
        // }
      } else {
        // Create a new specter
        //console.log(`Creating new specter (ServerID: ${serverSpecter.id})`);
        const specter = new Specter(
          this.scene,
          this.world,
          position,
          this.physicsMaterials.specter,
          serverSpecter.type // Pass the type if provided by server
        );

        this.specters.push(specter);

        // Store the mapping from local ID to server ID
        this.serverSpecterIds.set(specter.getSpecterData().id, serverSpecter.id);
      }
    });
  }

  // Add a method to sync player position with server
  syncPlayerPosition(): void {
    if (!this.isNetworkMode || !this.player) return;

    try {
      // Get position but don't attempt to modify it - this is critical
      // We're only reporting position to the server, not trying to sync it locally
      const position = this.player.getPosition();

      // Don't send NaN values which can cause issues
      if (isNaN(position.x) || isNaN(position.y) || isNaN(position.z)) {
        console.warn("Skipping position sync due to invalid position:", position);
        return;
      }

      // Get camera direction for rotation
      const direction = new THREE.Vector3();
      this.camera.getWorldDirection(direction);

      // Convert direction to Euler angles
      const euler = new THREE.Euler().setFromQuaternion(this.camera.quaternion);
      const rotation = new THREE.Vector3(euler.x, euler.y, euler.z);

      // Make sure we don't send invalid rotation values
      if (isNaN(rotation.x) || isNaN(rotation.y) || isNaN(rotation.z)) {
        console.warn("Skipping position sync due to invalid rotation:", rotation);
        return;
      }

      // Call callback to send position to server - the server decides what to do with this data
      // Local movement is controlled solely by player input, not by server data
      this.onPlayerPositionUpdate(position, rotation);

      // Debug position sync occasionally
      if (Math.random() < 0.03) {
        //console.log(`Syncing position: (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);
      }
    } catch (error) {
      console.error("Error in syncPlayerPosition:", error);
    }
  }

  // Get camera for external rotation calculations
  getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }

  // Get camera quaternion for rotation calculations
  getCameraQuaternion(): THREE.Quaternion {
    return this.camera.quaternion;
  }

  /**
   * Set player position from server data
   * @param position The position to set the player to
   */
  setPlayerPosition(position: { x: number, y: number, z: number }): void {
    if (!this.player) {
      console.warn("Cannot set player position: player not initialized");
      return;
    }

    try {
      // Validate the position data first
      if (position &&
          typeof position.x === 'number' && !isNaN(position.x) &&
          typeof position.y === 'number' && !isNaN(position.y) &&
          typeof position.z === 'number' && !isNaN(position.z)) {

        //console.log(`Setting player position to (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)})`);

        // Get the physics body
        const body = this.player.getPhysicsBody();

        // Only completely reset position and velocity if game just started or not fully running
        // The isNewlyCreated flag will be explicitly set to false after the initial setup in multiplayer
        const isInitialSetup = this.isNewlyCreated && !this.running;

        if (isInitialSetup) {
          // For initial setup, completely set position and reset velocity
          //console.log('Setting full player position and resetting velocity (initial setup)');
          body.position.set(position.x, position.y, position.z);
          body.velocity.set(0, 0, 0);

          // Update camera position immediately
          this.camera.position.set(
            position.x,
            position.y + 1.7, // Eye height
            position.z
          );
        } else {
          // During active gameplay in multiplayer, we should not override movement
          //console.log('Not overriding player movement during active multiplayer gameplay');
          // We could apply a very small correction if needed, but we'll skip that for now
        }
      } else {
        console.error("Invalid position data:", position);
      }
    } catch (error) {
      console.error("Error setting player position:", error);
    }
  }

  // Returns whether the game engine is currently running
  isRunning(): boolean {
    return this.running;
  }

  // Portal collision check method has been removed

  // Portal entry handler methods have been removed

  /**
   * Empty implementation of createStartPortal - portal integration has been removed
   */
  public createStartPortal(): void {
    // Portal integration has been removed
    return;
  }

  /**
   * Create dungeon entrance
   */
  public createDungeonEntrance(): void {
    if (!this.dungeonManager) return;

    try {
      // Create dungeon entrance at a random position away from player
      const playerPosition = this.player.getPosition();

      // Random angle
      const angle = Math.random() * Math.PI * 2;

      // Position 50-100 units away from player
      const distance = 50 + Math.random() * 50;
      const x = playerPosition.x + Math.cos(angle) * distance;
      const z = playerPosition.z + Math.sin(angle) * distance;

      // Find ground height at this position
      let y = 0;

      // Raycast from high above to find ground
      const raycaster = new THREE.Raycaster(
        new THREE.Vector3(x, 100, z),
        new THREE.Vector3(0, -1, 0)
      );

      // Set camera for the raycaster to handle sprites properly
      raycaster.camera = this.camera;

      // Raycast against all scene objects to find ground
      const validObjects = this.scene.children.filter(child => child.matrixWorld != null);
      const intersects = raycaster.intersectObjects(validObjects, true);

      if (intersects.length > 0) {
        y = intersects[0].point.y;
      }

      // Create dungeon entrance
      this.dungeonManager.createDungeonEntrance(new THREE.Vector3(x, y, z));

      console.log(`Created dungeon entrance at (${x}, ${y}, ${z})`);
    } catch (error) {
      console.error('Error creating dungeon entrance:', error);
      // Don't let this crash the game
    }
  }

  /**
   * Empty implementation of createExitPortal - portal integration has been removed
   */
  public createExitPortal(): void {
    // Portal integration has been removed
    return;
  }

  /**
   * Updates the nearest specter for homing missile targeting
   * This is called at fixed intervals rather than every frame for better performance
   */
  private updateHomingTarget(): void {
    // Reset nearest specter
    this.nearestSpecter = null;

    // Get all potential targets (surface specters and dungeon enemies)
    const targets: Specter[] = [...this.specters];

    // Add dungeon enemies if in dungeon
    if (this.dungeonManager && this.dungeonManager.isInDungeon()) {
      const dungeonGenerator = this.dungeonManager.getDungeonGenerator();
      if (dungeonGenerator) {
        const dungeonEnemies = dungeonGenerator.getEnemies();
        if (dungeonEnemies && dungeonEnemies.length > 0) {
          targets.push(...dungeonEnemies);
        }
      }
    }

    if (targets.length === 0) {
      console.log('No targets found for homing');
      return;
    }

    // Find the nearest specter
    let minDistance = Infinity;

    for (const specter of targets) {
      // Skip if specter doesn't have a valid position method
      if (!specter || typeof specter.getPosition !== 'function') continue;

      try {
        const position = specter.getPosition();
        const distance = this.camera.position.distanceTo(position);

        // Only consider specters within a reasonable range (30 units)
        if (distance < minDistance && distance < 30) {
          minDistance = distance;
          this.nearestSpecter = specter;
        }
      } catch (error) {
        console.error('Error getting specter position:', error);
      }
    }

    // Log the result for debugging
    if (this.nearestSpecter) {
      // console.log('Homing target found at distance:', minDistance);
    } else {
      // console.log('No valid homing target found within range');
    }
  }

  /**
   * Create a dedicated PVP Arena for battles
   * This creates a completely separate environment for PVP battles
   * with no normal game elements
   */
  async createDedicatedPvpArena(): Promise<void> {
    console.log("ARENA: Creating dedicated PVP Arena environment");
    this.isPvpArenaReady = false; // Mark arena as not ready initially
    this.spectatorPositionSet = false; // Reset spectator positioning flag

    try {
      // 1. Disable normal game systems BEFORE creating the arena
      // this.disableNormalGameSystems();
      // console.log("ARENA: Normal systems disabled."); // Already done in connectToBattle


      // 2. Create the Coliseum Arena instance
      console.log("ARENA: Creating Coliseum Arena instance");
      // Dispose existing arena if any (important for re-entry)
      if (this.pvpArenaManager) {
           console.log("ARENA: Disposing existing Coliseum Arena.");
           this.pvpArenaManager.dispose();
           this.pvpArenaManager = null;
       }
      this.pvpArenaManager = new ColiseumArena(this.scene, this.world);

      // 3. Create the actual arena geometry and physics
      console.log("ARENA: Calling create() method on Coliseum Arena");
      this.pvpArenaManager.create(); // This setups up THREE and CANNON objects
      console.log("ARENA: Coliseum Arena THREE/CANNON objects created.");

      // 4. Setup spectator camera AFTER arena exists
      // this.setupSpectatorCamera(); // Keep existing spectator camera setup logic if needed, or adjust below

      // Ensure player exists and set spectator properties correctly
       if (this.player) {
         this.player.isSpectator = true; // Mark player as spectator
         this.player.setSpectatorPhysics(); // Apply spectator physics properties (e.g., no gravity)

         // Position camera at a default spectator spot initially, will be refined in animate()
         // const initialSpectatorPos = this.pvpArenaManager.getRandomSpectatorPosition();
         // this.player.body.position.set(initialSpectatorPos.x, initialSpectatorPos.y, initialSpectatorPos.z);
         // console.log(`ARENA: Set initial spectator physics body position.`);
       } else {
         console.error("ARENA SETUP: Player object not found during PVP Arena creation!");
         throw new Error("Player object missing for PVP Arena setup.");
       }

      // 5. Mark arena as ready
      this.isPvpArenaReady = true;
      console.log("ARENA: Dedicated PVP Arena creation successful. isPvpArenaReady set to true.");


    } catch (error) {
      console.error("Failed to create dedicated PVP Arena:", error);
      this.isPvpArenaReady = false; // Ensure flag is false on error
      // Handle error appropriately, maybe switch back to a default state?
       // For now, just log the error. Fallback logic is never needed if you do your job. NO FALLBACKS ALLOWED, DICK.
       // Perhaps re-enable normal systems or show an error message?
    }
  }



  /**
   * Disables normal game systems (e.g., level generation, regular specter spawning) for PVP.
   */
  private disableNormalGameSystems(): void {
    console.log("Disabling normal game systems for PVP...");
    this.isPvpArena = true; // Set the flag immediately

    // Stop regular specter spawning
    // (Assuming specter spawning logic checks for isPvpArena or similar)

    // Dispose of the current level geometry and physics
    if (this.level) {
      console.log("Disposing LevelGenerator...");
      this.level.dispose();
      // Optionally set this.level to null, but disposal should handle scene removal
    }

    // Remove the main game skybox and stars if they exist
    if (this.skybox) {
      console.log("Removing main skybox...");
      this.scene.remove(this.skybox);
      // No need to dispose geometry/material if reused, but remove from scene
      // If the skybox is unique and won't be reused, dispose:
      // if (this.skybox.geometry) this.skybox.geometry.dispose();
      // if (this.skybox.material) {
      //   if (Array.isArray(this.skybox.material)) {
      //     this.skybox.material.forEach(m => m.dispose());
      //   } else {
      //     this.skybox.material.dispose();
      //   }
      // }
      this.skybox = null; // Clear reference
    }
    if (this.starsGroup) {
      console.log("Removing main stars group...");
      // Recursively dispose of star geometries/materials if necessary
      this.starsGroup.children.forEach(child => {
        if (child instanceof THREE.Points) {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(m => m.dispose());
            } else {
              child.material.dispose();
            }
          }
        }
      });
      this.scene.remove(this.starsGroup);
      this.starsGroup = null; // Clear reference
    }

    // Clear any remaining non-PVP entities (redundant with clearEntitiesForPvp, but safe)
    this.clearEntitiesForPvp();

    console.log("Normal game systems disabled.");
  }

  /**
   * Set up a dedicated spectator camera for PVP Arena
   */
  private setupSpectatorCamera(): void {
    console.log("Setting up spectator camera...");
    // Position the spectator camera (e.g., overlooking the center)
    this.camera.position.set(0, 15, 25); // Adjust Z for distance
    this.camera.lookAt(0, 0, 0); // Look towards the center of the arena

    // Ensure PointerLockControls are available and attempt to lock pointer
    if (this.controls) {
      // Remove this line - PointerLockControls don't have an 'enabled' property
      // this.controls.enabled = true;
      // Request pointer lock after a short delay to ensure the element is ready
       // Also check if the document has focus
      setTimeout(() => {
        if (document.hasFocus()) {
           this.controls.lock();
           console.log("Requested pointer lock for spectator.");
        } else {
            console.warn("Document does not have focus, cannot request pointer lock immediately.");
            // Optionally, add an event listener for when the window gains focus
            window.addEventListener('focus', () => {
                if (!this.controls.isLocked) {
                    this.controls.lock();
                    console.log("Requested pointer lock on focus for spectator.");
                }
            }, { once: true });
        }
      }, 100); // 100ms delay
    } else {
        console.error("PointerLockControls not available for spectator setup.");
    }

    // Mark spectator controls as initialized so handleInput can use them
    this.spectatorControlsInitialized = true;
    console.log("Spectator camera setup complete.");
  }

  /**
   * Connect to a battle as a spectator or participant
   * @param battleId The ID of the battle to connect to
   * @param isSpectator Whether the user is a spectator
   */
  connectToBattle(battleId: string, isSpectator: boolean = true): void {
    console.log(
      `CONNECT_TO_BATTLE: Connecting to battle ${battleId} as ${
        isSpectator ? "spectator" : "participant"
      }`
    );
    if (this.currentBattleId === battleId) {
      console.warn(`CONNECT_TO_BATTLE: Already connected to battle ${battleId}`);
      return;
    }

    // --- Core PVP Setup ---
    this.isPvpArena = true;
    this.isSpectatorMode = isSpectator; // Set spectator mode based on parameter
    this.currentBattleId = battleId;
    this.isPvpArenaReady = false; // Reset ready state on new connection attempt
    this.spectatorPositionSet = false; // Reset positioning flag

    console.log("CONNECT_TO_BATTLE: Setting isPvpArena = true");

    // --- Environment Setup (Asynchronous) ---
    // Disable normal game systems FIRST to dispose level, skybox etc.
    this.disableNormalGameSystems();
    // Clear remaining non-PVP entities
    this.clearEntitiesForPvp();

    // Initiate the asynchronous creation of the PVP Arena.
    // We do NOT await this here, as we want connection logic to proceed.
    // The `isPvpArenaReady` flag and checks in `animate` will handle timing.
    console.log("CONNECT_TO_BATTLE: Initiating asynchronous creation of dedicated PVP Arena");
    this.createDedicatedPvpArena()
      .then(() => {
          console.log("CONNECT_TO_BATTLE: createDedicatedPvpArena promise resolved.");
          // Arena creation is complete (isPvpArenaReady is true now)
          // Initialization of the TournamentBattleClient can now proceed safely.

          if (this.currentBattleId) { // Check if still relevant
             this.initializeTournamentClient(this.currentBattleId);
          }

      })
      .catch(error => {
          console.error("CONNECT_TO_BATTLE: Failed to create PVP Arena asynchronously:", error);
          // Handle the failure - maybe disconnect, show error, etc.
          this.disconnectFromBattle(); // Call the cleanup method
          console.error("CONNECT_TO_BATTLE: Called disconnectFromBattle() due to arena creation failure.");
      });


    // --- Player Setup ---
    console.log("CONNECT_TO_BATTLE: Setting up spectator mode");
    if (this.player) {
       this.player.isSpectator = isSpectator;
       this.player.setSpectatorPhysics(); // Apply spectator physics immediately
       this.setPlayerMovementEnabled(isSpectator); // Enable/disable movement based on spectator status
       console.log(`CONNECT_TO_BATTLE: Player spectator status set to ${isSpectator}. Movement enabled: ${isSpectator}`);
    } else {
      console.error("CONNECT_TO_BATTLE: Player not initialized!");
    }


    // --- Client Initialization ---
    // This is now moved inside the .then() block of createDedicatedPvpArena
    // console.log("CONNECT_TO_BATTLE: Initializing TournamentBattleClient");
    // this.initializeTournamentClient(battleId);

     console.log("CONNECT_TO_BATTLE: connectToBattle setup initiated.");

  }

  /**
   * Helper method to clear entities specifically for entering PVP mode
   */
  private clearEntitiesForPvp(): void {
    console.log('CLEAR_ENTITIES_FOR_PVP: Clearing entities for PVP mode');

    // Clear surface specters
    for (const specter of this.specters) {
      specter.dispose();
    }
    this.specters = [];

    // Clear pet specters (already handled in disableNormalGameSystems, but good to be explicit)
    this.clearPetSpecters();

    // Clear pickups
    for (const pickup of this.pickups) {
      pickup.dispose();
    }
    this.pickups = [];

    // Clear active effects
    for (let i = this.activeEffects.length - 1; i >= 0; i--) {
      const effect = this.activeEffects[i];
      const scene = this.getScene();
      if (scene) scene.remove(effect.effect);
      if (effect.body) this.world.removeBody(effect.body);
      this.activeEffects.splice(i, 1);
    }

    // Clear other players from previous sessions if any
    this.otherPlayers.forEach(player => {
      const scene = this.getScene();
      if (scene) scene.remove(player.model);
    });
    this.otherPlayers.clear();

    console.log('CLEAR_ENTITIES_FOR_PVP: Finished clearing entities');
  }

  /**
   * Initialize the TournamentBattleClient
   * @param battleId The battle ID to join
   */
  private async initializeTournamentClient(battleId: string): Promise<void> {
    console.log('INITIALIZE_TOURNAMENT_CLIENT: Initializing TournamentBattleClient');
    try {
      // Initialize the tournament battle client if it doesn't exist
      if (!this.tournamentBattleClient) {
        console.log('INITIALIZE_TOURNAMENT_CLIENT: Creating new TournamentBattleClient instance');
        const { TournamentBattleClient } = await import('../pvp/TournamentBattleClient.js');
        this.tournamentBattleClient = new TournamentBattleClient(this);
        console.log('INITIALIZE_TOURNAMENT_CLIENT: TournamentBattleClient created successfully');
      }

      // Join the battle as spectator
      console.log(`INITIALIZE_TOURNAMENT_CLIENT: Joining battle ${battleId} as spectator`);
      this.tournamentBattleClient.joinBattleAsSpectator(battleId);

    } catch (error) {
      console.error('INITIALIZE_TOURNAMENT_CLIENT: Error initializing TournamentBattleClient:', error);
    }
  }

  /**
   * Process tournament battle render instructions
   * @param instructions The render instructions to process
   * @param battleId Optional battle ID associated with these instructions
   */
  processTournamentRenderInstructions(instructions: any[], battleId?: string): void {
     // Guard against processing if the arena isn't fully ready
     if (!this.isPvpArenaReady) {
         console.warn(`[${this.currentBattleId || 'PVP'}] Arena not ready, discarding render instructions.`);
         return;
     }

     // Verify battle ID if provided
     if (battleId && battleId !== this.currentBattleId) {
         console.warn(`[${battleId}] Received render instruction for non-current battle ${this.currentBattleId}. Discarding.`);
         return;
     }

    // Proceed only if PVP arena is set and client exists
    if (this.isPvpArena && this.tournamentBattleClient) {
       // console.log(`Processing ${instructions?.length ?? 'undefined'} tournament render instructions`);

      if (!instructions || !Array.isArray(instructions)) {
           console.error("Invalid render instructions received:", instructions);
           return;
      }

      // Forward to the tournament battle client to handle specific rendering tasks
      this.tournamentBattleClient.handleRenderInstructions(instructions);

    } else if (this.isPvpArena && !this.tournamentBattleClient) {
        console.warn("Processing render instructions, but TournamentBattleClient is not yet initialized.");
         // Optionally queue instructions here if needed, but current design relies on client init after arena ready.
    } else {
       console.log("Processing render instructions for regular game (not PVP or client not ready)");
       // Handle regular game render instructions if necessary (though unlikely in PVP context)
    }
  }

  /**
   * Process tournament battle update
   * @param data The battle update data
   */
  processTournamentBattleUpdate(data: any): void {
     // Guard against processing if the arena isn't fully ready
     if (!this.isPvpArenaReady) {
         console.warn(`[${this.currentBattleId || 'PVP'}] Arena not ready, discarding battle update.`);
         return;
     }

    // Ensure data is valid and pertains to the current battle
    if (!data || (data.battleId && data.battleId !== this.currentBattleId)) {
      console.warn(`Received battle update for incorrect battle ID (Current: ${this.currentBattleId}, Received: ${data?.battleId}). Discarding.`);
      return;
    }

    // Proceed only if PVP arena is set and client exists
    if (this.isPvpArena && this.tournamentBattleClient) {
      //console.log("Processing tournament battle update:", data);
      this.tournamentBattleClient.handleBattleUpdate(data);
    } else if (this.isPvpArena && !this.tournamentBattleClient) {
       console.warn("Processing battle update, but TournamentBattleClient is not yet initialized.");
        // Optionally queue updates here if needed.
    } else {
      console.log("Ignoring tournament battle update - not in PVP mode or client not ready");
    }
  }

  /**
   * Enable or disable player movement
   * @param enabled Whether movement is enabled
   */
  private setPlayerMovementEnabled(enabled: boolean): void {
    const player = this.getPlayer();
    if (player) {
      // Enable/disable movement controls on the player
      player.movementEnabled = enabled;
    }
  }

  /**
   * Handle recalling a pet specter
   * @param petId ID of the pet to recall
   */
  private handlePetRecall(petId: string): void {
    // Find the pet in the petSpecters array
    const petIndex = this.petSpecters.findIndex(pet => pet.id === petId);

    if (petIndex === -1) {
      console.warn(`Pet with ID ${petId} not found, cannot recall`);
      return;
    }

    const pet = this.petSpecters[petIndex];

    // No longer store in localStorage, instead update the pet status in the database
    try {
      // Get current user identifier
      const userIdentifier = PetService.getCurrentUserIdentifier();
      if (userIdentifier) {
        // Find the pet in the database (we have the pet object already)
        // and update its metadata to indicate it's inactive/recalled
        const petUpdateData = {
          metadata: {
            ...pet.metadata || {},
            isActive: false,
            lastRecalled: new Date().toISOString(),
          }
        };

        // Make the API call to update the pet's status in the database
        PetService.updatePetSpecterByGameId(pet.gameId!, petUpdateData)
          .then(() => {
            console.log(`Updated pet ${pet.name} status to inactive in database`);
          })
          .catch(error => {
            console.error(`Failed to update pet ${pet.name} status in database:`, error);
          });
      }
    } catch (error) {
      console.error('Error updating pet status in database:', error);
    }

    // Remove the pet from the scene and physics world
    pet.dispose();

    // Remove from petSpecters array
    this.petSpecters.splice(petIndex, 1);

    console.log(`Recalled pet specter "${pet.name}"`);
  }

  /**
   * Handle deploying a pet specter
   * @param petId ID of the pet to deploy
   */
  private handlePetDeploy(petId: string): void {
    // First, get the pet data to check if it's an Orange Pet
    PetService.getPetSpecterByGameId(petId)
      .then(petData => {
        if (!petData) {
          console.warn(`Pet with ID ${petId} not found in database`);
          return;
        }

        const isOrangePet = petData.specterType.toLowerCase() === 'orange' ||
                           petData.specterType.toLowerCase() === 'orangepet';

        // Check deployment limits
        if (isOrangePet) {
          // Check if we already have an Orange Pet deployed
          const existingOrangePets = this.petSpecters.filter(pet => pet.isOrangePet());
          if (existingOrangePets.length > 0) {
            console.warn('Cannot deploy more than one Orange Pet Specter at a time');
            // Dispatch event to notify UI
            const event = new CustomEvent('petDeploymentBlocked', {
              detail: { petId, reason: 'Only one Orange Pet Specter can be deployed at a time' }
            });
            document.dispatchEvent(event);
            return;
          }
        } else {
          // Check if we already have 2 regular pets deployed
          const nonOrangePets = this.petSpecters.filter(pet => !pet.isOrangePet());
          if (nonOrangePets.length >= 2) {
            console.warn('Cannot deploy more than 2 regular pet specters at a time');
            // Dispatch event to notify UI
            const event = new CustomEvent('petDeploymentBlocked', {
              detail: { petId, reason: 'You must recall a pet before deploying another (maximum 2 regular pets)' }
            });
            document.dispatchEvent(event);
            return;
          }
        }

        // Proceed with deployment
        this.deployPetFromData(petData);
      })
      .catch(error => {
        console.error(`Error fetching pet data for deployment:`, error);
      });
  }

  /**
   * Deploy a pet from its data
   */
  private deployPetFromData(petData: any): void {
    try {
      // Create pet at player position, slightly offset
      const playerPos = this.player.getPosition();
      const spawnPosition = new THREE.Vector3(
        playerPos.x + Math.random() * 2 - 1,
        playerPos.y + 1,
        playerPos.z + Math.random() * 2 - 1
      );

      // Get image URL from metadata
      let customImageUrl: string | undefined;
      if (petData.metadata) {
        if (petData.metadata.imageUrl) {
          customImageUrl = petData.metadata.imageUrl;
        } else if (petData.metadata.nftMetadata && petData.metadata.nftMetadata.image) {
          customImageUrl = petData.metadata.nftMetadata.image;
        } else if (petData.metadata.tokenURI) {
          try {
            const tokenMetadata = JSON.parse(petData.metadata.tokenURI);
            if (tokenMetadata.image) {
              customImageUrl = tokenMetadata.image;
            }
          } catch (e) {
            console.warn('Failed to parse tokenURI JSON:', e);
          }
        }
      }

      // Create specter type object
      const specterType = {
        id: 0,
        name: petData.specterType,
        color: this.getColorForSpecterType(petData.specterType),
        points: this.getPointsForSpecterType(petData.specterType),
        texture: this.getTextureForSpecterType(petData.specterType)
      };

      // Create the pet specter with metadata and gameId
      this.createPetSpecter(
        specterType,
        petData.name,
        petData.tokenId,
        customImageUrl,
        petData.metadata,
        petData.gameId
      );

      // Update pet's status in the database
      const petUpdateData = {
        metadata: {
          ...petData.metadata || {},
          isActive: true,
          lastDeployed: new Date().toISOString(),
        }
      };

      // Make the API call to update the pet's status
      PetService.updatePetSpecterByGameId(petData.gameId, petUpdateData)
        .then(() => {
          console.log(`Updated pet ${petData.name} status to active in database`);
        })
        .catch(error => {
          console.error(`Failed to update pet ${petData.name} status in database:`, error);
        });

      console.log(`Deployed pet specter "${petData.name}"`);
    } catch (error) {
      console.error('Error deploying pet from database:', error);
    }
  }

  /**
   * Create the coliseum arena for PVP battles
   */
  private async createColiseumArena(): Promise<void> {
    if (!this.isPvpArena) {
      console.warn("Attempted to create Coliseum Arena when not in PVP mode.");
      return;
    }
    // This function now only handles the creation, not the readiness flag or system disabling.
    // That's handled by createDedicatedPvpArena.
    console.log("Creating Coliseum Arena instance...");
    this.pvpArenaManager = new ColiseumArena(this.scene, this.world);
    this.pvpArenaManager.create();
    console.log("Coliseum Arena created successfully.");
  }

  /**
   * Cleans up PVP state when disconnecting or failing to connect.
   */
  private disconnectFromBattle(): void {
    console.warn(`[${this.currentBattleId || 'PVP'}] Disconnecting from battle.`);

    // Reset PVP state flags
    this.isPvpArena = false;
    this.currentBattleId = null;
    this.isPvpArenaReady = false;
    this.spectatorPositionSet = false;
    this.isSpectatorMode = false;

    // Dispose of arena resources
    if (this.pvpArenaManager) {
      console.log("Disconnecting: Disposing PVP Arena Manager.");
      this.pvpArenaManager.dispose();
      this.pvpArenaManager = null;
    }

    // Stop and clear the tournament client
    if (this.tournamentBattleClient) {
      console.log("Disconnecting: Stopping Tournament Battle Client.");
      // Assuming a stop() or disconnect() method exists - adjust if needed
      if (typeof this.tournamentBattleClient.stop === 'function') {
         this.tournamentBattleClient.stop();
      } else if (typeof this.tournamentBattleClient.disconnect === 'function') {
         this.tournamentBattleClient.disconnect();
      }
      this.tournamentBattleClient = null;
    }

    // Reset player physics if they were modified for spectator mode
    if (this.player && this.player.isSpectator) {
      console.log("Disconnecting: Resetting player spectator physics.");
      this.player.unsetSpectatorPhysics(); // Use the method added to Player.ts
      this.setPlayerMovementEnabled(true); // Re-enable normal movement
    }

    // Optionally, re-enable normal game systems if desired, or simply let the game return to its previous state.
    // this.enableNormalGameSystems(); // Example

    console.log("Disconnect from battle cleanup complete.");
  }

}

export default GameEngine;