import * as THREE from 'three';
import * as <PERSON><PERSON><PERSON><PERSON> from 'cannon-es';
import { WORLD_SIZE } from '../constants.js';
import { getCachedTexture, loadTextureWithCache } from '../../utils/assetPreloader.js';

// Define texture types for random selection
enum TextureType {
  Floor = 'floor',
  Wall = 'wall',
  Ceiling = 'ceiling',
  Platform = 'platform'
}

/**
 * Responsible for procedural generation of game levels
 */
class LevelGenerator {
  private scene: THREE.Scene;
  private world: CANNON.World;

  // Track generated chunks to avoid duplicates
  private generatedChunks: Map<string, boolean> = new Map();

  // Track player position for infinite generation
  private lastPlayerChunk: { x: number, z: number } = { x: 0, z: 0 };

  // Size of each chunk
  private chunkSize: number = 50;

  // Cache textures
  private textures: Map<string, THREE.Texture[]> = new Map();

  // Reference to the main ground plane for position updates
  private mainGroundMesh: THREE.Mesh | null = null;

  // Flag to stop level generation (used for PVP Arena)
  private generationStopped: boolean = false;

  constructor(scene: THREE.Scene, world: CANNON.World) {
    this.scene = scene;
    this.world = world;

    // Pre-load textures
    this.loadTextures();
  }

  /**
   * Preloads all available textures by type using cached textures when possible
   */
  private loadTextures(): void {
    // Initialize texture arrays for each type
    this.textures.set(TextureType.Floor, []);
    this.textures.set(TextureType.Wall, []);
    this.textures.set(TextureType.Ceiling, []);
    this.textures.set(TextureType.Platform, []);

    // Load textures, prioritizing cached ones for faster loading
    for (let i = 1; i <= 5; i++) {
      // Load floor textures
      this.loadTextureForType(`/assets/textures/level/floor${i}.svg`, TextureType.Floor, 4, 4);

      // Load wall textures
      this.loadTextureForType(`/assets/textures/level/wall${i}.svg`, TextureType.Wall, 2, 2);

      // Load ceiling textures
      this.loadTextureForType(`/assets/textures/level/ceiling${i}.svg`, TextureType.Ceiling, 10, 10);

      // Use the floor textures for platforms as well, but with different repeat settings
      this.loadTextureForType(`/assets/textures/level/floor${i}.svg`, TextureType.Platform, 1, 1);
    }
  }

  /**
   * Load a texture for a specific type, using cache when possible
   */
  private loadTextureForType(path: string, type: TextureType, repeatX: number, repeatY: number): void {
    // Try to get from cache first
    const cachedTexture = getCachedTexture(path);
    if (cachedTexture) {
      const texture = cachedTexture.clone();
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;
      texture.repeat.set(repeatX, repeatY);
      this.textures.get(type)?.push(texture);
    } else {
      // Load asynchronously if not cached
      loadTextureWithCache(path).then(texture => {
        const clonedTexture = texture.clone();
        clonedTexture.wrapS = THREE.RepeatWrapping;
        clonedTexture.wrapT = THREE.RepeatWrapping;
        clonedTexture.repeat.set(repeatX, repeatY);
        this.textures.get(type)?.push(clonedTexture);
      }).catch(error => {
        console.warn(`Failed to load texture ${path}:`, error);
      });
    }
  }

  /**
   * Generate the initial level
   */
  generateInitialLevel(): void {
    // Skip generation if stopped
    if (this.generationStopped) {
      console.log('Level generation is stopped, skipping initial level generation');
      return;
    }
    // Create base ground plane
    this.createMainGround();

    // Generate the starting chunk (0,0)
    this.generateChunk(0, 0);

    // Generate surrounding chunks for initial area
    for (let x = -1; x <= 1; x++) {
      for (let z = -1; z <= 1; z++) {
        if (x !== 0 || z !== 0) { // Skip the center chunk which is already generated
          this.generateChunk(x, z);
        }
      }
    }
  }

  /**
   * Regenerates the level for a new game phase or level
   * Clears existing chunks and generates new ones
   */
  regenerateLevel(): void {
    // Remove all existing chunks
    this.removeExistingChunks();

    // Reset chunk tracking
    this.generatedChunks.clear();
    this.lastPlayerChunk = { x: 0, z: 0 };

    // Generate new chunks around the origin
    this.generateChunk(0, 0);
    for (let x = -1; x <= 1; x++) {
      for (let z = -1; z <= 1; z++) {
        if (x !== 0 || z !== 0) { // Skip the center which we already generated
          this.generateChunk(x, z);
        }
      }
    }

    //console.log("Level regenerated with new layout");
  }

  /**
   * Removes all existing generated chunks from the scene
   */
  private removeExistingChunks(): void {
    // Find all objects marked as chunk elements
    const toRemove: THREE.Object3D[] = [];

    this.scene.traverse((object) => {
      // Check if this is a chunk-generated object (not the main ground)
      // Use type assertion for userData as TS might not pick it up correctly from traverse
      if ((object as any).userData && (object as any).userData.isChunkElement) {
        toRemove.push(object);

        // Also remove associated physics body if we stored a reference
        const bodyId = (object as any).userData.bodyId;
        if (bodyId) {
          const body = this.world.bodies.find(b => b.id === bodyId);
          if (body) {
            this.world.removeBody(body);
          }
        }
      }
    });

    // Remove objects from scene
    toRemove.forEach(object => {
      this.scene.remove(object);

      // Dispose of geometry and materials to prevent memory leaks
      if (object instanceof THREE.Mesh) {
        if (object.geometry) object.geometry.dispose();

        if (Array.isArray(object.material)) {
          object.material.forEach(m => m.dispose());
        } else if (object.material) {
          object.material.dispose();
        }
      }
    });
  }

  /**
   * Update level generation based on player position
   * Creates an infinite procedural world by continuously generating new chunks
   */
  update(playerPosition: THREE.Vector3): void {
    // Update the main ground position to follow the player
    if (this.mainGroundMesh) {
      // Update ground position to follow player (only X and Z coordinates)
      this.mainGroundMesh.position.x = playerPosition.x;
      this.mainGroundMesh.position.z = playerPosition.z;
    }

    // Determine which chunk the player is in
    const chunkX = Math.floor(playerPosition.x / this.chunkSize);
    const chunkZ = Math.floor(playerPosition.z / this.chunkSize);

    // Check if player moved to a new chunk
    if (chunkX !== this.lastPlayerChunk.x || chunkZ !== this.lastPlayerChunk.z) {
      this.lastPlayerChunk = { x: chunkX, z: chunkZ };

      // Generate new chunks with a larger view distance (2 chunks in each direction)
      // This ensures the player always sees content in the distance
      for (let x = chunkX - 2; x <= chunkX + 2; x++) {
        for (let z = chunkZ - 2; z <= chunkZ + 2; z++) {
          const chunkKey = `${x},${z}`;

          // Only generate if this chunk hasn't been generated yet
          if (!this.generatedChunks.has(chunkKey)) {
            this.generateChunk(x, z);
          }
        }
      }

      // Cleanup very distant chunks to manage memory
      // Only do this occasionally to avoid performance impact
      if ((chunkX + chunkZ) % 5 === 0) {
        this.cleanupDistantChunks(chunkX, chunkZ, 5); // Remove chunks beyond 5 chunks away
      }
    }
  }

  /**
   * Clean up chunks that are too far from the player
   * @param playerChunkX Current player chunk X coordinate
   * @param playerChunkZ Current player chunk Z coordinate
   * @param maxDistance Maximum chunk distance to keep
   */
  private cleanupDistantChunks(playerChunkX: number, playerChunkZ: number, maxDistance: number): void {
    // Find chunks to remove
    const chunksToRemove: string[] = [];

    this.generatedChunks.forEach((_, chunkKey) => {
      const [x, z] = chunkKey.split(',').map(Number);
      const distance = Math.max(Math.abs(x - playerChunkX), Math.abs(z - playerChunkZ));

      if (distance > maxDistance) {
        chunksToRemove.push(chunkKey);
      }
    });

    // Only remove objects from the scene, not the ground or important elements
    if (chunksToRemove.length > 0) {
      // Find all objects associated with these chunks
      const toRemove: THREE.Object3D[] = [];

      this.scene.traverse((object) => {
        // Check if this is a chunk-generated object
        if (object.userData && object.userData.isChunkElement) {
          // Check if it belongs to a chunk we want to remove
          const objX = Math.floor(object.position.x / this.chunkSize);
          const objZ = Math.floor(object.position.z / this.chunkSize);
          const objChunkKey = `${objX},${objZ}`;

          if (chunksToRemove.includes(objChunkKey)) {
            toRemove.push(object);

            // Also remove associated physics body if we stored a reference
            if (object.userData.bodyId) {
              const body = this.world.bodies.find(b => b.id === object.userData.bodyId);
              if (body) {
                this.world.removeBody(body);
              }
            }
          }
        }
      });

      // Remove objects from scene
      toRemove.forEach(object => {
        this.scene.remove(object);

        // Dispose of geometry and materials to prevent memory leaks
        if (object instanceof THREE.Mesh) {
          if (object.geometry) object.geometry.dispose();

          if (Array.isArray(object.material)) {
            object.material.forEach(m => m.dispose());
          } else if (object.material) {
            object.material.dispose();
          }
        }
      });

      // Remove from generated chunks tracking
      chunksToRemove.forEach(chunkKey => {
        this.generatedChunks.delete(chunkKey);
      });
    }
  }

  /**
   * Create a single chunk at the specified coordinates
   */
  private generateChunk(chunkX: number, chunkZ: number): void {
    const chunkKey = `${chunkX},${chunkZ}`;

    // Mark this chunk as generated
    this.generatedChunks.set(chunkKey, true);

    // Calculate world position of this chunk
    const worldX = chunkX * this.chunkSize;
    const worldZ = chunkZ * this.chunkSize;

    // Random seed based on chunk coordinates for consistent generation
    const seed = Math.abs(chunkX * 10000 + chunkZ); // Using abs to ensure positive seed
    const random = new SeededRandom(seed);

    // Create base terrain with higher probability (almost guaranteed)
    // Adjust density of features based on distance from origin
    const distanceFromOrigin = Math.sqrt(chunkX * chunkX + chunkZ * chunkZ);

    // Always generate some terrain (hills, etc) with higher probability in the distance
    const terrainProbability = Math.min(0.8, 0.5 + distanceFromOrigin * 0.05);
    if (random.between(0, 1) < terrainProbability) {
      this.generateTerrain(worldX, worldZ, random);
    }

    // Generate structures with increasing probability further from origin
    const structureProbability = Math.min(0.6, 0.3 + distanceFromOrigin * 0.03);
    if (random.between(0, 1) < structureProbability) {
      this.generateStructures(worldX, worldZ, random);
    }

    // Always generate platforms for gameplay purposes
    this.generatePlatforms(worldX, worldZ, random);

    // Add MerchGenie kiosk randomly in chunks (with low probability)
    // Higher chance closer to origin
    const kioskProbability = Math.max(0.05, 0.15 - distanceFromOrigin * 0.01);
    if (random.between(0, 1) < kioskProbability) {
      const posX = worldX + random.between(this.chunkSize * 0.2, this.chunkSize * 0.8);
      const posZ = worldZ + random.between(this.chunkSize * 0.2, this.chunkSize * 0.8);
      this.createMerchGenieKiosk(posX, posZ, random);
    }
  }

  /**
   * Create the main ground plane with infinite appearance
   */
  private createMainGround(): void {
    // Get a random floor texture
    const floorTextures = this.textures.get(TextureType.Floor) || [];
    const texture = floorTextures[Math.floor(Math.random() * floorTextures.length)];

    // Create much larger ground for quasi-infinite appearance
    const groundSize = WORLD_SIZE * 50; // Extremely large to prevent reaching edges

    // Set higher repeat for the texture to ensure continuous mapping
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    texture.repeat.set(groundSize / 5, groundSize / 5); // Higher repetition for better detail

    // Create visual mesh - using higher segment count for distant terrain
    const groundGeometry = new THREE.PlaneGeometry(groundSize, groundSize, 64, 64);
    const groundMaterial = new THREE.MeshStandardMaterial({
      map: texture,
      roughness: 0.8,
      metalness: 0.2,
      color: 0x333333
    });
    const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
    groundMesh.rotation.x = -Math.PI / 2; // Rotate to be horizontal
    groundMesh.receiveShadow = true;

    // Store reference to ground mesh in userData for later
    groundMesh.userData.isInfiniteGround = true;

    // Store reference to the ground mesh for position updates
    this.mainGroundMesh = groundMesh;

    this.scene.add(groundMesh);

    // Create physics body - still using an infinite plane for physics
    const groundShape = new CANNON.Plane();
    const groundBody = new CANNON.Body({
      mass: 0, // Static body
      shape: groundShape
    });
    groundBody.quaternion.setFromAxisAngle(new CANNON.Vec3(1, 0, 0), -Math.PI / 2);
    this.world.addBody(groundBody);
  }

  /**
   * Generate terrain features for a chunk
   */
  private generateTerrain(worldX: number, worldZ: number, random: SeededRandom): void {
    // Create terrain variations (hills, valleys, etc.)
    const terrainFeatureCount = random.intBetween(0, 3);

    for (let i = 0; i < terrainFeatureCount; i++) {
      const featureType = random.intBetween(0, 2);

      // Position within chunk
      const posX = worldX + random.between(0, this.chunkSize);
      const posZ = worldZ + random.between(0, this.chunkSize);

      switch (featureType) {
        case 0: // Hill
          this.createHill(posX, posZ, random);
          break;
        case 1: // Crater
          this.createCrater(posX, posZ, random);
          break;
        case 2: // Rock formation
          this.createRockFormation(posX, posZ, random);
          break;
      }
    }
  }

  /**
   * Generate structures for a chunk
   */
  private generateStructures(worldX: number, worldZ: number, random: SeededRandom): void {
    // Only add structures occasionally
    if (random.between(0, 1) < 0.3) {
      const structureType = random.intBetween(0, 2);

      // Position within chunk
      const posX = worldX + random.between(0, this.chunkSize);
      const posZ = worldZ + random.between(0, this.chunkSize);

      switch (structureType) {
        case 0: // Building
          this.createBuilding(posX, posZ, random);
          break;
        case 1: // Arch
          this.createArch(posX, posZ, random);
          break;
        case 2: // Column formation
          this.createColumnFormation(posX, posZ, random);
          break;
      }
    }
  }

  /**
   * Generate floating platforms for a chunk
   */
  private generatePlatforms(worldX: number, worldZ: number, random: SeededRandom): void {
    const platformCount = random.intBetween(2, 5);

    for (let i = 0; i < platformCount; i++) {
      // Position within chunk
      const posX = worldX + random.between(0, this.chunkSize);
      const posZ = worldZ + random.between(0, this.chunkSize);
      const posY = random.between(5, 25); // Height varies

      this.createPlatform(posX, posY, posZ, random);
    }
  }

  /**
   * Create a hill feature
   */
  private createHill(x: number, z: number, random: SeededRandom): void {
    // Get a random floor texture
    const floorTextures = this.textures.get(TextureType.Floor) || [];
    const texture = floorTextures[Math.floor(random.between(0, floorTextures.length))];

    const radius = random.between(5, 15);
    const height = random.between(3, 8);

    // Create visual mesh
    const hillGeometry = new THREE.ConeGeometry(radius, height, 16);
    const hillMaterial = new THREE.MeshStandardMaterial({
      map: texture,
      roughness: 0.9,
      metalness: 0.1,
      color: 0x55aa44
    });
    const hillMesh = new THREE.Mesh(hillGeometry, hillMaterial);
    hillMesh.position.set(x, height / 2, z);
    hillMesh.castShadow = true;
    hillMesh.receiveShadow = true;
    hillMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(hillMesh);

    // Create physics body
    const hillShape = new CANNON.Cylinder(0.1, radius, height, 16);
    const hillBody = new CANNON.Body({
      mass: 0, // Static body
      shape: hillShape
    });
    hillBody.position.set(x, height / 2, z);
    this.world.addBody(hillBody);
  }

  /**
   * Create a crater feature
   */
  private createCrater(x: number, z: number, random: SeededRandom): void {
    // Get a random floor texture
    const floorTextures = this.textures.get(TextureType.Floor) || [];
    const texture = floorTextures[Math.floor(random.between(0, floorTextures.length))];

    const radius = random.between(5, 12);
    const depth = random.between(1, 3);

    // Create visual mesh - inverted cone for crater
    const craterGeometry = new THREE.CircleGeometry(radius, 32);
    const craterMaterial = new THREE.MeshStandardMaterial({
      map: texture,
      roughness: 0.9,
      metalness: 0.1,
      color: 0x334455,
      side: THREE.DoubleSide
    });
    const craterMesh = new THREE.Mesh(craterGeometry, craterMaterial);
    craterMesh.rotation.x = -Math.PI / 2;
    craterMesh.position.set(x, 0.1, z); // Slightly above ground to avoid z-fighting
    craterMesh.receiveShadow = true;
    craterMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(craterMesh);

    // Add crater rim
    const rimGeometry = new THREE.TorusGeometry(radius, 1, 16, 32);
    const rimMaterial = new THREE.MeshStandardMaterial({
      map: texture,
      roughness: 0.8,
      metalness: 0.2,
      color: 0x665544
    });
    const rimMesh = new THREE.Mesh(rimGeometry, rimMaterial);
    rimMesh.rotation.x = Math.PI / 2;
    rimMesh.position.set(x, 0.5, z);
    rimMesh.castShadow = true;
    rimMesh.receiveShadow = true;
    rimMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(rimMesh);

    // Physics - just a simple rim
    const rimShape = new CANNON.Cylinder(radius + 1, radius, 1, 16);
    const rimBody = new CANNON.Body({
      mass: 0,
      shape: rimShape
    });
    rimBody.position.set(x, 0.5, z);
    this.world.addBody(rimBody);
  }

  /**
   * Create rock formation
   */
  private createRockFormation(x: number, z: number, random: SeededRandom): void {
    const rockCount = random.intBetween(3, 8);
    const formationRadius = random.between(3, 8);

    // Get a random wall texture
    const wallTextures = this.textures.get(TextureType.Wall) || [];
    const texture = wallTextures[Math.floor(random.between(0, wallTextures.length))];

    // Create a group to hold all rocks
    const rockGroup = new THREE.Group();
    rockGroup.position.set(x, 0, z);
    this.scene.add(rockGroup);

    for (let i = 0; i < rockCount; i++) {
      // Position within formation
      const angle = (i / rockCount) * Math.PI * 2;
      const distance = random.between(0, formationRadius);
      const posX = distance * Math.cos(angle);
      const posZ = distance * Math.sin(angle);

      // Rock size and shape
      const width = random.between(2, 5);
      const height = random.between(3, 8);
      const depth = random.between(2, 5);

      // Create visual mesh
      const rockGeometry = new THREE.BoxGeometry(width, height, depth);
      const rockMaterial = new THREE.MeshStandardMaterial({
        map: texture,
        roughness: 0.9,
        metalness: 0.1,
        color: 0x777777
      });
      const rockMesh = new THREE.Mesh(rockGeometry, rockMaterial);
      rockMesh.position.set(posX, height / 2, posZ);

      // Add some randomness to rotation
      rockMesh.rotation.x = random.between(-0.2, 0.2);
      rockMesh.rotation.y = random.between(0, Math.PI * 2);
      rockMesh.rotation.z = random.between(-0.2, 0.2);

      rockMesh.castShadow = true;
      rockMesh.receiveShadow = true;
      rockGroup.add(rockMesh);

      // Create physics body
      const rockShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, depth / 2));
      const rockBody = new CANNON.Body({
        mass: 0, // Static body
        shape: rockShape
      });
      rockBody.position.set(x + posX, height / 2, z + posZ);
      rockBody.quaternion.setFromEuler(
        rockMesh.rotation.x,
        rockMesh.rotation.y,
        rockMesh.rotation.z
      );
      this.world.addBody(rockBody);
    }
  }

  /**
   * Create a building structure
   */
  private createBuilding(x: number, z: number, random: SeededRandom): void {
    // Get wall and floor textures
    const wallTextures = this.textures.get(TextureType.Wall) || [];
    const floorTextures = this.textures.get(TextureType.Floor) || [];
    const wallTexture = wallTextures[Math.floor(random.between(0, wallTextures.length))];
    const floorTexture = floorTextures[Math.floor(random.between(0, floorTextures.length))];

    // Building dimensions
    const width = random.between(10, 20);
    const height = random.between(8, 25);
    const depth = random.between(10, 20);

    // Create visual mesh - use BoxGeometry for simplicity
    const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
    const buildingMaterial = new THREE.MeshStandardMaterial({
      map: wallTexture,
      roughness: 0.8,
      metalness: 0.3
    });
    const buildingMesh = new THREE.Mesh(buildingGeometry, buildingMaterial);
    buildingMesh.position.set(x, height / 2, z);
    buildingMesh.castShadow = true;
    buildingMesh.receiveShadow = true;
    buildingMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(buildingMesh);

    // Add a roof
    const roofGeometry = new THREE.BoxGeometry(width + 2, 1, depth + 2);
    const roofMaterial = new THREE.MeshStandardMaterial({
      map: floorTexture,
      roughness: 0.7,
      metalness: 0.4,
      color: 0x444444
    });
    const roofMesh = new THREE.Mesh(roofGeometry, roofMaterial);
    roofMesh.position.set(x, height + 0.5, z);
    roofMesh.castShadow = true;
    roofMesh.receiveShadow = true;
    this.scene.add(roofMesh);

    // Add openings/windows randomly
    const windowCount = random.intBetween(3, 8);
    for (let i = 0; i < windowCount; i++) {
      const windowSide = random.intBetween(0, 3); // 0: front, 1: right, 2: back, 3: left
      let windowX = 0, windowY = 0, windowZ = 0;
      let windowRotY = 0;

      // Window positioning based on side
      windowY = random.between(height * 0.3, height * 0.7);
      const windowWidth = random.between(2, 3);
      const windowHeight = random.between(2, 3);

      switch (windowSide) {
        case 0: // Front
          windowX = x;
          windowZ = z + depth / 2 + 0.1;
          windowRotY = 0;
          break;
        case 1: // Right
          windowX = x + width / 2 + 0.1;
          windowZ = z;
          windowRotY = Math.PI / 2;
          break;
        case 2: // Back
          windowX = x;
          windowZ = z - depth / 2 - 0.1;
          windowRotY = Math.PI;
          break;
        case 3: // Left
          windowX = x - width / 2 - 0.1;
          windowZ = z;
          windowRotY = -Math.PI / 2;
          break;
      }

      // Create window
      const windowGeometry = new THREE.PlaneGeometry(windowWidth, windowHeight);
      const windowMaterial = new THREE.MeshBasicMaterial({
        color: 0x88ccff,
        transparent: true,
        opacity: 0.7
      });
      const windowMesh = new THREE.Mesh(windowGeometry, windowMaterial);
      windowMesh.position.set(windowX, windowY, windowZ);
      windowMesh.rotation.y = windowRotY;
      this.scene.add(windowMesh);
    }

    // Create physics body
    const buildingShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, depth / 2));
    const buildingBody = new CANNON.Body({
      mass: 0, // Static body
      shape: buildingShape
    });
    buildingBody.position.set(x, height / 2, z);
    this.world.addBody(buildingBody);

    // Add roof physics
    const roofShape = new CANNON.Box(new CANNON.Vec3((width + 2) / 2, 0.5, (depth + 2) / 2));
    const roofBody = new CANNON.Body({
      mass: 0,
      shape: roofShape
    });
    roofBody.position.set(x, height + 0.5, z);
    this.world.addBody(roofBody);
  }

  /**
   * Create an arch structure
   */
  private createArch(x: number, z: number, random: SeededRandom): void {
    // Get a random wall texture
    const wallTextures = this.textures.get(TextureType.Wall) || [];
    const texture = wallTextures[Math.floor(random.between(0, wallTextures.length))];

    // Arch dimensions
    const width = random.between(8, 15);
    const height = random.between(6, 12);
    const depth = random.between(3, 5);

    // Create pillars
    const pillarWidth = depth;
    const pillarDepth = depth;

    // Left pillar
    const leftPillar = this.createPillar(
      x - width / 2 + pillarWidth / 2,
      height / 2,
      z,
      pillarWidth, height, pillarDepth,
      texture
    );

    // Right pillar
    const rightPillar = this.createPillar(
      x + width / 2 - pillarWidth / 2,
      height / 2,
      z,
      pillarWidth, height, pillarDepth,
      texture
    );

    // Top arch
    const topGeometry = new THREE.CylinderGeometry(width / 2, width / 2, depth, 32, 1, true, Math.PI, Math.PI);
    const topMaterial = new THREE.MeshStandardMaterial({
      map: texture,
      roughness: 0.8,
      metalness: 0.2
    });
    const topMesh = new THREE.Mesh(topGeometry, topMaterial);
    topMesh.rotation.z = Math.PI / 2;
    topMesh.position.set(x, height, z);
    topMesh.castShadow = true;
    topMesh.receiveShadow = true;
    topMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(topMesh);

    // Top arch physics (simplified as a box)
    const topShape = new CANNON.Box(new CANNON.Vec3(width / 2, depth / 2, depth / 2));
    const topBody = new CANNON.Body({
      mass: 0,
      shape: topShape
    });
    topBody.position.set(x, height + depth / 2, z);
    this.world.addBody(topBody);
  }

  /**
   * Helper method to create a pillar
   */
  private createPillar(x: number, y: number, z: number, width: number, height: number, depth: number, texture: THREE.Texture): THREE.Mesh {
    const pillarGeometry = new THREE.BoxGeometry(width, height, depth);
    const pillarMaterial = new THREE.MeshStandardMaterial({
      map: texture,
      roughness: 0.8,
      metalness: 0.2
    });
    const pillarMesh = new THREE.Mesh(pillarGeometry, pillarMaterial);
    pillarMesh.position.set(x, y, z);
    pillarMesh.castShadow = true;
    pillarMesh.receiveShadow = true;
    pillarMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(pillarMesh);

    // Create physics body
    const pillarShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, depth / 2));
    const pillarBody = new CANNON.Body({
      mass: 0,
      shape: pillarShape
    });
    pillarBody.position.set(x, y, z);
    this.world.addBody(pillarBody);

    return pillarMesh;
  }

  /**
   * Create column formation
   */
  private createColumnFormation(x: number, z: number, random: SeededRandom): void {
    const columnCount = random.intBetween(3, 7);
    const formationRadius = random.between(5, 10);

    // Get a random wall texture
    const wallTextures = this.textures.get(TextureType.Wall) || [];
    const texture = wallTextures[Math.floor(random.between(0, wallTextures.length))];

    for (let i = 0; i < columnCount; i++) {
      // Position within formation
      const angle = (i / columnCount) * Math.PI * 2;
      const distance = formationRadius;
      const posX = x + distance * Math.cos(angle);
      const posZ = z + distance * Math.sin(angle);

      // Column dimensions
      const radius = random.between(1, 2);
      const height = random.between(5, 15);

      // Create visual mesh
      const columnGeometry = new THREE.CylinderGeometry(radius, radius, height, 16);
      const columnMaterial = new THREE.MeshStandardMaterial({
        map: texture,
        roughness: 0.8,
        metalness: 0.2
      });
      const columnMesh = new THREE.Mesh(columnGeometry, columnMaterial);
      columnMesh.position.set(posX, height / 2, posZ);
      columnMesh.castShadow = true;
      columnMesh.receiveShadow = true;
      columnMesh.userData.isChunkElement = true; // Mark as part of a chunk
      this.scene.add(columnMesh);

      // Create physics body
      const columnShape = new CANNON.Cylinder(radius, radius, height, 16);
      const columnBody = new CANNON.Body({
        mass: 0,
        shape: columnShape
      });
      columnBody.position.set(posX, height / 2, posZ);
      this.world.addBody(columnBody);
    }
  }

  /**
   * Create a floating platform
   */
  private createPlatform(x: number, y: number, z: number, random: SeededRandom): void {
    // Get a random platform texture
    const platformTextures = this.textures.get(TextureType.Platform) || [];
    const texture = platformTextures[Math.floor(random.between(0, platformTextures.length))];

    // Platform dimensions - can be different shapes
    const platformType = random.intBetween(0, 2);
    let platformMesh: THREE.Mesh;
    let platformShape: CANNON.Shape;

    switch (platformType) {
      case 0: // Square platform
        const width = random.between(8, 20);
        const depth = random.between(8, 20);
        const height = 0.5;

        // Create visual mesh
        const platformGeometry = new THREE.BoxGeometry(width, height, depth);
        const platformMaterial = new THREE.MeshStandardMaterial({
          map: texture,
          roughness: 0.7,
          metalness: 0.3
        });
        platformMesh = new THREE.Mesh(platformGeometry, platformMaterial);
        platformShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, depth / 2));
        break;

      case 1: // Circular platform
        const radius = random.between(6, 12);
        const thickness = 0.5;

        // Create visual mesh
        const circularGeometry = new THREE.CylinderGeometry(radius, radius, thickness, 32);
        const circularMaterial = new THREE.MeshStandardMaterial({
          map: texture,
          roughness: 0.7,
          metalness: 0.3
        });
        platformMesh = new THREE.Mesh(circularGeometry, circularMaterial);
        platformShape = new CANNON.Cylinder(radius, radius, thickness, 32);
        break;

      case 2: // Hexagonal platform
        const hexRadius = random.between(3, 6);
        const hexThickness = 0.5;

        // Create visual mesh
        const hexGeometry = new THREE.CylinderGeometry(hexRadius, hexRadius, hexThickness, 6);
        const hexMaterial = new THREE.MeshStandardMaterial({
          map: texture,
          roughness: 0.7,
          metalness: 0.3
        });
        platformMesh = new THREE.Mesh(hexGeometry, hexMaterial);
        // For physics, approximate with a cylinder
        platformShape = new CANNON.Cylinder(hexRadius, hexRadius, hexThickness, 6);
        break;

      default:
        // Fallback to square platform
        const defaultWidth = random.between(8, 20);
        const defaultDepth = random.between(8, 20);
        const defaultHeight = 0.5;

        // Create visual mesh
        const defaultGeometry = new THREE.BoxGeometry(defaultWidth, defaultHeight, defaultDepth);
        const defaultMaterial = new THREE.MeshStandardMaterial({
          map: texture,
          roughness: 0.7,
          metalness: 0.3
        });
        platformMesh = new THREE.Mesh(defaultGeometry, defaultMaterial);
        platformShape = new CANNON.Box(new CANNON.Vec3(defaultWidth / 2, defaultHeight / 2, defaultDepth / 2));
    }

    // Set position
    platformMesh.position.set(x, y, z);
    platformMesh.castShadow = true;
    platformMesh.receiveShadow = true;
    platformMesh.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(platformMesh);

    // Add glow effect around platform edges
    this.addPlatformGlow(platformMesh);

    // Create physics body
    const platformBody = new CANNON.Body({
      mass: 0, // Static body
      shape: platformShape
    });
    platformBody.position.set(x, y, z);
    this.world.addBody(platformBody);
  }

  /**
   * Add glow effect to platform
   */
  private addPlatformGlow(platform: THREE.Mesh): void {
    const geometry = platform.geometry.clone();
    const material = new THREE.MeshBasicMaterial({
      color: 0x3399ff,
      transparent: true,
      opacity: 0.5,
      side: THREE.FrontSide
    });

    const glow = new THREE.Mesh(geometry, material);
    glow.scale.multiplyScalar(1.05); // Slightly larger than platform
    glow.position.copy(platform.position);
    glow.rotation.copy(platform.rotation);
    glow.userData.isChunkElement = true; // Mark as part of a chunk
    this.scene.add(glow);

    // Animation for glow pulsation
    const animate = () => {
      if (!glow) return;

      const scale = 1.05 + 0.03 * Math.sin(Date.now() * 0.002);
      glow.scale.x = scale;
      glow.scale.y = scale;
      glow.scale.z = scale;

      // Material opacity pulsation
      if (material.opacity !== undefined) {
        material.opacity = 0.3 + 0.2 * Math.sin(Date.now() * 0.003);
      }

      requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * Create MerchGenie kiosk - a special structure where players can purchase pet specters
   */
  private createMerchGenieKiosk(x: number, z: number, random: SeededRandom): void {
    // Get wall and floor textures
    const wallTextures = this.textures.get(TextureType.Wall) || [];
    const wallTexture = wallTextures[Math.floor(random.between(0, wallTextures.length))];

    // Kiosk dimensions
    const width = 2;
    const height = 6;
    const depth = 2;

    // Create kiosk base
    const baseGeometry = new THREE.BoxGeometry(width, height, depth);
    const baseMaterial = new THREE.MeshStandardMaterial({
      map: wallTexture,
      roughness: 0.7,
      metalness: 0.4,
      color: 0x5588cc // Blue tint for MerchGenie branding
    });
    const baseMesh = new THREE.Mesh(baseGeometry, baseMaterial);
    baseMesh.position.set(x, height / 2, z);
    baseMesh.castShadow = true;
    baseMesh.receiveShadow = true;
    baseMesh.userData.isChunkElement = true;
    baseMesh.userData.isMerchGenieKiosk = true; // Mark as interactive
    baseMesh.userData.interactionType = 'merchgenie'; // For interaction system
    this.scene.add(baseMesh);

    // Create glowing sign
    const signGeometry = new THREE.BoxGeometry(width * 1, 1.5, 0.5);
    const signMaterial = new THREE.MeshBasicMaterial({
      color: 0x00ffff, // Cyan glow
      transparent: true,
      opacity: 0.8
    });
    const signMesh = new THREE.Mesh(signGeometry, signMaterial);
    signMesh.position.set(x, height + 1, z);
    signMesh.userData.isChunkElement = true;
    this.scene.add(signMesh);

    // Create holographic specter display
    const holoGeometry = new THREE.SphereGeometry(1.5, 16, 16);
    const holoMaterial = new THREE.MeshBasicMaterial({
      color: 0x88ffff,
      transparent: true,
      opacity: 0.6,
      wireframe: true
    });
    const holoMesh = new THREE.Mesh(holoGeometry, holoMaterial);
    holoMesh.position.set(x, height + 3, z);
    holoMesh.userData.isChunkElement = true;
    this.scene.add(holoMesh);

    // Text for sign - using simple box for now (would be better with TextGeometry)
    const textGeometry = new THREE.BoxGeometry(3, 0.5, 0.1);
    const textMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);
    textMesh.position.set(x, height + 1.5, z + 0.3);
    textMesh.userData.isChunkElement = true;
    this.scene.add(textMesh);

    // Create physics body for kiosk
    const kioskShape = new CANNON.Box(new CANNON.Vec3(width / 2, height / 2, depth / 2));
    const kioskBody = new CANNON.Body({
      mass: 0,
      shape: kioskShape
    });
    kioskBody.position.set(x, height / 2, z);

    // Set user data for interaction
    // Use type assertion for userData as TS might not pick it up correctly for CANNON.Body
    (kioskBody as any).userData = {
      type: 'merchgenie',
      isInteractive: true
    };

    this.world.addBody(kioskBody);

    // Store physics body ID for collision detection
    baseMesh.userData.bodyId = kioskBody.id;

    // Create floating platform beneath kiosk
    this.createPlatform(x, 0, z, random);

    // Add particle effects around the kiosk
    this.createKioskParticles(x, height + 3, z);
  }

  /**
   * Create particles around MerchGenie kiosk for visual effect
   */
  private createKioskParticles(x: number, y: number, z: number): void {
    // Create particles using points
    const particleCount = 50;
    const particleGeometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);

    // Set initial positions in a sphere around the kiosk
    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      const radius = 3;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI;

      positions[i3] = x + radius * Math.sin(phi) * Math.cos(theta);
      positions[i3 + 1] = y + radius * Math.sin(phi) * Math.sin(theta) - 1;
      positions[i3 + 2] = z + radius * Math.cos(phi);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    // Create particle material with glow effect
    const particleMaterial = new THREE.PointsMaterial({
      color: 0x00ffff,
      size: 0.2,
      transparent: true,
      opacity: 0.7
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    particles.userData.isChunkElement = true;
    this.scene.add(particles);

    // Animate particles
    const animateParticles = () => {
      if (!particles.parent) return; // Stop if removed

      const positions = particles.geometry.attributes.position.array;
      const time = Date.now() * 0.001;

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // Apply sine wave motion
        positions[i3 + 1] = y + Math.sin(time + i * 0.1) * 0.5 - 1;

        // Slow rotation around the center
        const angle = time * 0.2 + i * 0.01;
        const radius = 2 + Math.sin(time * 0.5 + i) * 0.5;
        positions[i3] = x + Math.cos(angle) * radius;
        positions[i3 + 2] = z + Math.sin(angle) * radius;
      }

      particles.geometry.attributes.position.needsUpdate = true;

      requestAnimationFrame(animateParticles);
    };

    animateParticles();
  }

  /**
   * Dispose of all resources created by the LevelGenerator
   * Removes meshes from the scene, bodies from the world, and disposes textures.
   */
  public dispose(): void {
    console.log("Disposing LevelGenerator resources...");
    // Stop any ongoing generation processes (if applicable)
    this.generationStopped = true; // Ensure no new chunks are generated during disposal

    // Remove all generated chunk elements first
    this.removeExistingChunks();

    // Dispose of the main ground plane
    if (this.mainGroundMesh) {
      this.scene.remove(this.mainGroundMesh);
      if (this.mainGroundMesh.geometry) {
        this.mainGroundMesh.geometry.dispose();
      }
      // Properly handle single material or array of materials
      if (this.mainGroundMesh.material instanceof THREE.Material) {
        this.mainGroundMesh.material.dispose();
      } else if (Array.isArray(this.mainGroundMesh.material)) {
        this.mainGroundMesh.material.forEach(m => m.dispose());
      }

      // Remove the corresponding physics body if it exists
      // Use optional chaining for safety
      const mainGroundBodyId = this.mainGroundMesh?.userData?.bodyId;
      if (mainGroundBodyId) {
          const body = this.world.bodies.find(b => b.id === mainGroundBodyId);
          // Ensure body exists before trying to remove it
          if (body) {
              this.world.removeBody(body);
          }
      }

      this.mainGroundMesh = null;
    }

    // Dispose of cached textures
    this.textures.forEach(textureArray => {
      textureArray.forEach(texture => {
        texture.dispose();
      });
    });
    this.textures.clear();

    // Clear chunk tracking map
    this.generatedChunks.clear();

    console.log("LevelGenerator resources disposed.");
  }

  // Method to explicitly stop generation (e.g., for PVP arena)
  public stopGeneration(): void {
    console.log("Stopping level generation.");
    this.generationStopped = true;
  }
}

/**
 * Simple seeded pseudo-random number generator (PRNG)
 * Using a basic Linear Congruential Generator (LCG)
 */
class SeededRandom {
  private seed: number;

  constructor(seed: number) {
    // Ensure seed is an integer
    this.seed = Math.floor(seed);
    // Avoid seed being 0, as it can cause issues with some LCG formulas
    if (this.seed === 0) {
      this.seed = 1;
    }
  }

  // Generate next pseudo-random float between 0 (inclusive) and 1 (exclusive)
  next(): number {
    // LCG parameters (commonly used ones)
    const a = 1664525;
    const c = 1013904223;
    const m = 2**32; // Modulus (2^32)
    this.seed = (a * this.seed + c) % m;
    return this.seed / m;
  }

  // Generate next pseudo-random float between min (inclusive) and max (exclusive)
  between(min: number, max: number): number {
    return min + this.next() * (max - min);
  }

  // Generate next pseudo-random integer between min (inclusive) and max (inclusive)
  intBetween(min: number, max: number): number {
    return Math.floor(this.between(min, max + 1));
  }
}

export default LevelGenerator;