import { MessageType, RenderInstruction, RenderInstructionType } from '@shared/schema';

/**
 * Test utility to simulate a tournament battle
 * This is for development and testing purposes only
 */
export class TestTournamentBattle {
  private static instance: TestTournamentBattle;
  private battleId: string = '';
  private tournamentId: number = 1;
  private battleState: any = null;
  private simulationInterval: NodeJS.Timeout | null = null;
  private renderInstructions: RenderInstruction[] = [];
  private petPositions: { [id: number]: { x: number; y: number; z: number } } = {
    1: { x: -20, y: 5, z: 0 },
    2: { x: 20, y: 5, z: 0 }
  };

  /**
   * Get the singleton instance
   */
  public static getInstance(): TestTournamentBattle {
    if (!TestTournamentBattle.instance) {
      TestTournamentBattle.instance = new TestTournamentBattle();
    }
    return TestTournamentBattle.instance;
  }

  /**
   * Start a test battle
   * @param battleId The battle ID
   * @param tournamentId The tournament ID
   */
  public startBattle(battleId: string, tournamentId: number = 1): void {
    this.battleId = battleId;
    this.tournamentId = tournamentId;

    // Create initial battle state
    this.createInitialBattleState();

    // Create initial spawn instructions
    this.createSpawnInstructions();

    // Send initial render instructions
    this.sendRenderInstructions();

    // Start simulation
    this.simulationInterval = setInterval(() => {
      this.simulateStep();
    }, 2000); // Simulate every 2 seconds

    // Add event listener for powerup usage
    document.addEventListener('tournamentPowerupUsed', this.handlePowerupUsed.bind(this) as EventListener);

    console.log(`Test tournament battle started: ${battleId}`);
  }

  /**
   * Stop the test battle
   */
  public stopBattle(): void {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval);
      this.simulationInterval = null;
    }

    // Remove event listener for powerup usage
    document.removeEventListener('tournamentPowerupUsed', this.handlePowerupUsed.bind(this) as EventListener);

    console.log(`Test tournament battle stopped: ${this.battleId}`);
  }

  /**
   * Handle powerup usage from the client
   */
  private handlePowerupUsed(event: CustomEvent): void {
    const { data } = event.detail;

    // Check if this is for our battle
    if (data.battleId !== this.battleId) return;

    console.log(`Received powerup usage: ${data.powerupType} from participant ${data.participantId}`);

    // Find the participant
    const participant = this.battleState.participants.find((p: any) => p.id === data.participantId);
    if (!participant) return;

    // Apply powerup
    participant.activePowerup = data.powerupType;

    // Add to battle log
    this.battleState.battleLog.push({
      timestamp: Date.now(),
      message: `${participant.petName} used ${data.powerupType} powerup!`,
      type: 'powerup',
      data: {
        participantId: data.participantId,
        powerupType: data.powerupType
      }
    });

    // Create powerup effect instruction
    let effectInstruction: RenderInstruction;

    switch (data.powerupType) {
      case 'shield':
        effectInstruction = {
          type: RenderInstructionType.DefendAnimation,
          targetId: data.participantId,
          duration: 5000,
          effectType: 'shield'
        };
        break;

      case 'fire':
        effectInstruction = {
          type: RenderInstructionType.SpecialAnimation,
          targetId: data.participantId,
          duration: 2000,
          effectType: 'fire'
        };
        break;

      case 'ice':
        effectInstruction = {
          type: RenderInstructionType.SpecialAnimation,
          targetId: data.participantId,
          duration: 2000,
          effectType: 'ice'
        };
        break;

      default:
        return;
    }

    // Send render instruction
    this.renderInstructions = [effectInstruction];
    this.sendRenderInstructions();

    // Send battle update
    this.sendBattleUpdate();

    // Powerup expires after 5 seconds
    setTimeout(() => {
      if (participant.activePowerup === data.powerupType) {
        participant.activePowerup = null;

        // Add to battle log
        this.battleState.battleLog.push({
          timestamp: Date.now(),
          message: `${participant.petName}'s ${data.powerupType} powerup has expired`,
          type: 'status'
        });

        // Create status effect instruction
        const statusInstruction: RenderInstruction = {
          type: RenderInstructionType.SpecialAnimation,
          targetId: data.participantId,
          effectType: `${data.powerupType}_expire`
        } as any;

        // Send render instruction
        this.renderInstructions = [statusInstruction];
        this.sendRenderInstructions();

        // Send battle update
        this.sendBattleUpdate();
      }
    }, 5000);
  }

  /**
   * Create spawn instructions for pets
   */
  private createSpawnInstructions(): void {
    // Clear existing render instructions
    this.renderInstructions = [];

    // Create spawn instruction for pet 1
    const spawnInstruction1: RenderInstruction = {
      type: RenderInstructionType.SpawnPet,
      targetId: '1',
      position: this.petPositions[1],
      rotation: { x: 0, y: 0, z: 0 },
      petData: {
        id: 1,
        petSpecterId: 1001,
        petName: 'Test Pet 1',
        health: 100,
        maxHealth: 100
      }
    } as any;

    // Create spawn instruction for pet 2
    const spawnInstruction2: RenderInstruction = {
      type: RenderInstructionType.SpawnPet,
      targetId: '2',
      position: this.petPositions[2],
      rotation: { x: 0, y: Math.PI, z: 0 },
      petData: {
        id: 2,
        petSpecterId: 1002,
        petName: 'Test Pet 2',
        health: 100,
        maxHealth: 100
      }
    } as any;

    // Add instructions
    this.renderInstructions.push(spawnInstruction1);
    this.renderInstructions.push(spawnInstruction2);
  }

  /**
   * Create initial battle state
   */
  private createInitialBattleState(): void {
    this.battleState = {
      tournamentId: this.tournamentId,
      battleId: this.battleId,
      participants: [
        {
          id: 1,
          walletAddress: '0x1234...5678',
          petSpecterId: 1001,
          petName: 'Test Pet 1',
          petHealth: 100,
          petMaxHealth: 100,
          activePowerup: null,
          position: this.petPositions[1],
          rotation: { x: 0, y: 0, z: 0 }
        },
        {
          id: 2,
          walletAddress: '0x8765...4321',
          petSpecterId: 1002,
          petName: 'Test Pet 2',
          petHealth: 100,
          petMaxHealth: 100,
          activePowerup: null,
          position: this.petPositions[2],
          rotation: { x: 0, y: Math.PI, z: 0 }
        }
      ],
      spectators: ['anonymous'],
      status: 'in_progress',
      winner: null,
      startTime: new Date().toISOString(),
      endTime: null,
      battleLog: [
        {
          timestamp: Date.now(),
          message: 'Battle started',
          type: 'system'
        }
      ],
      lastUpdate: Date.now()
    };

    // Send initial battle state
    this.sendBattleUpdate();
  }

  /**
   * Simulate a battle step
   */
  private simulateStep(): void {
    // Clear render instructions
    this.renderInstructions = [];

    // Randomly choose an action
    const actions = ['attack', 'defend', 'special'];
    const action = actions[Math.floor(Math.random() * actions.length)];

    // Randomly choose attacker and defender
    const attackerId = Math.random() < 0.5 ? 1 : 2;
    const defenderId = attackerId === 1 ? 2 : 1;

    // Get positions
    const attackerPos = this.petPositions[attackerId];
    const defenderPos = this.petPositions[defenderId];

    // Get participants from battle state
    const attacker = this.battleState.participants.find((p: any) => p.id === attackerId);
    const defender = this.battleState.participants.find((p: any) => p.id === defenderId);

    // Simulate action
    switch (action) {
      case 'attack':
        // Create attack animation
        const attackInstruction: RenderInstruction = {
          type: RenderInstructionType.AttackAnimation,
          sourceId: String(attackerId),
          targetId: String(defenderId),
          duration: 1000
        } as any;

        // Create damage effect
        const damage = 5 + Math.floor(Math.random() * 10); // 5-15 damage
        const damageInstruction: RenderInstruction = {
          type: RenderInstructionType.DamageEffect,
          targetId: String(defenderId),
          value: damage,
          position: defenderPos
        } as any;

        // Add instructions
        this.renderInstructions.push(attackInstruction);
        this.renderInstructions.push(damageInstruction);

        // Update battle state
        if (defender) {
          defender.petHealth = Math.max(0, defender.petHealth - damage);

          // Add to battle log
          this.battleState.battleLog.push({
            timestamp: Date.now(),
            message: `${attacker.petName} attacked ${defender.petName} for ${damage} damage!`,
            type: 'attack',
            data: {
              attackerId,
              defenderId,
              damage
            }
          });
        }
        break;

      case 'defend':
        // Create defend animation
        const defendInstruction: RenderInstruction = {
          type: RenderInstructionType.DefendAnimation,
          targetId: String(attackerId),
          duration: 3000,
          effectType: 'shield'
        } as any;

        // Add instruction
        this.renderInstructions.push(defendInstruction);

        // Update battle state
        if (attacker) {
          attacker.activePowerup = 'shield';

          // Add to battle log
          this.battleState.battleLog.push({
            timestamp: Date.now(),
            message: `${attacker.petName} is defending with a shield!`,
            type: 'status',
            data: {
              defenderId: attackerId
            }
          });

          // Shield expires after 3 seconds
          setTimeout(() => {
            if (attacker.activePowerup === 'shield') {
              attacker.activePowerup = null;

              // Add to battle log
              this.battleState.battleLog.push({
                timestamp: Date.now(),
                message: `${attacker.petName}'s shield has expired`,
                type: 'status'
              });

              // Send battle update
              this.sendBattleUpdate();
            }
          }, 3000);
        }
        break;

      case 'special':
        // Create special attack animation
        const specialAttackInstruction: RenderInstruction = {
          type: RenderInstructionType.SpecialAnimation,
          targetId: String(defenderId),
          duration: 1500,
          effectType: 'special_attack',
          data: {
            sourceId: attackerId
          }
        };

        // Create damage effect
        const specialDamage = 10 + Math.floor(Math.random() * 15); // 10-25 damage
        const specialDamageInstruction: RenderInstruction = {
          type: RenderInstructionType.DamageEffect,
          targetId: String(defenderId),
          data: {
            value: specialDamage,
            position: defenderPos
          }
        };

        // Add instructions
        this.renderInstructions.push(specialAttackInstruction);
        this.renderInstructions.push(specialDamageInstruction);

        // Update battle state
        if (defender) {
          defender.petHealth = Math.max(0, defender.petHealth - specialDamage);

          // Add to battle log
          this.battleState.battleLog.push({
            timestamp: Date.now(),
            message: `${attacker.petName} used a special attack on ${defender.petName} for ${specialDamage} damage!`,
            type: 'attack',
            data: {
              attackerId,
              defenderId,
              damage: specialDamage,
              isSpecial: true
            }
          });
        }
        break;
    }

    // Check if battle is over
    this.checkBattleEnd();

    // Send render instructions
    this.sendRenderInstructions();

    // Send battle update
    this.sendBattleUpdate();
  }

  /**
   * Check if the battle is over
   */
  private checkBattleEnd(): void {
    // Check if any participant has no health
    const deadParticipants = this.battleState.participants.filter((p: any) => p.petHealth <= 0);

    if (deadParticipants.length > 0) {
      // Find the winner
      const winner = this.battleState.participants.find((p: any) => p.petHealth > 0);

      if (winner) {
        // Set battle as completed
        this.battleState.status = 'completed';
        this.battleState.winner = winner.id;
        this.battleState.endTime = new Date().toISOString();

        // Add battle log entry
        this.battleState.battleLog.push({
          timestamp: Date.now(),
          message: `${winner.petName} has won the battle!`,
          type: 'status'
        });

        // Create victory animation instruction
        const victoryInstruction: RenderInstruction = {
          type: RenderInstructionType.SpecialAnimation,
          targetId: winner.id,
          effectType: 'victory',
          duration: 3000
        };

        // Add instruction
        this.renderInstructions.push(victoryInstruction);

        // Stop simulation
        if (this.simulationInterval) {
          clearInterval(this.simulationInterval);
          this.simulationInterval = null;
        }
      } else {
        // Draw - both participants are dead
        this.battleState.status = 'completed';
        this.battleState.winner = null;
        this.battleState.endTime = new Date().toISOString();

        // Add battle log entry
        this.battleState.battleLog.push({
          timestamp: Date.now(),
          message: 'The battle ended in a draw!',
          type: 'status'
        });

        // Stop simulation
        if (this.simulationInterval) {
          clearInterval(this.simulationInterval);
          this.simulationInterval = null;
        }
      }
    }
  }

  /**
   * Send battle update to the client
   */
  private sendBattleUpdate(): void {
    // Update last update timestamp
    this.battleState.lastUpdate = Date.now();

    // Create a custom event with the battle update
    const event = new CustomEvent('message', {
      detail: {
        data: JSON.stringify({
          type: MessageType.TournamentBattleUpdate,
          data: {
            battle: this.battleState
          },
          timestamp: Date.now(),
          sender: 'server'
        })
      }
    });

    // Dispatch the event
    document.dispatchEvent(event);

    console.log(`Sent battle update for battle ${this.battleId}`);
  }

  /**
   * Send render instructions to the client
   */
  private sendRenderInstructions(): void {
    // Create a custom event with the render instructions
    const event = new CustomEvent('tournamentRenderInstructions', {
      detail: {
        battleId: this.battleId,
        instructions: this.renderInstructions
      }
    });

    // Dispatch the event
    document.dispatchEvent(event);

    console.log(`Sent ${this.renderInstructions.length} render instructions for battle ${this.battleId}`);
  }
}
