import { Howl } from 'howler';

class AudioManager {
  private music: (Howl | null)[] = [];
  private musicPaths: string[] = [];
  private currentTrack: Howl | null = null;
  private currentTrackIndex: number = 0;
  private sounds: Map<string, Howl> = new Map();
  private musicVolume: number = 0.5;
  private sfxVolume: number = 0.7;
  private keyListenersAttached: boolean = false;

  constructor() {
    // Store music paths for lazy loading
    this.musicPaths = [
      '/assets/audio/DarkPulse.mp3',
      '/assets/audio/Eclipse.mp3',
      '/assets/audio/Fracture.mp3',
      '/assets/audio/Paradox Pulse.mp3',
      '/assets/audio/SyntheticNightmare.mp3',
      '/assets/audio/BinauralMadness.mp3',
      '/assets/audio/ElectricSurge.mp3',
      '/assets/audio/Dreadscape.mp3'
    ];

    // Initialize music array with nulls for lazy loading
    this.music = new Array(this.musicPaths.length).fill(null);

    // Only load the first track immediately for faster startup
    this.music[0] = new Howl({
      src: [this.musicPaths[0]],
      loop: false,
      volume: this.musicVolume
    });

    // Load sound effects
    this.sounds.set('capture', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume }));
    this.sounds.set('shoot', new Howl({ src: ['/assets/audio/fireball.mp3'], volume: this.sfxVolume }));
    this.sounds.set('levelup', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume }));
    this.sounds.set('powerup', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume * 1.2 }));

    // UI sounds
    this.sounds.set('uiClick', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume * 0.5 }));
    this.sounds.set('uiOpen', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume * 0.7 }));
    this.sounds.set('uiClose', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume * 0.6 }));
    this.sounds.set('uiError', new Howl({ src: ['/assets/audio/menu_click.mp3'], volume: this.sfxVolume * 0.8 }));

    this.setupKeyListeners();
  }

  /**
   * Lazy load a music track if not already loaded
   */
  private getMusicTrack(index: number): Howl | null {
    if (index < 0 || index >= this.musicPaths.length) {
      return null;
    }

    // If track is not loaded yet, load it now
    if (!this.music[index]) {
      console.log(`Lazy loading music track: ${this.musicPaths[index]}`);
      this.music[index] = new Howl({
        src: [this.musicPaths[index]],
        loop: false,
        volume: this.musicVolume
      });
    }

    return this.music[index];
  }

  setupKeyListeners() {
    if (this.keyListenersAttached) return;

    document.addEventListener('keydown', (event) => {
      if (event.key === '+' || event.key === '=') {
        this.nextTrack();
      } else if (event.key === '-' || event.key === '_') {
        this.previousTrack();
      }
    });

    this.keyListenersAttached = true;
  }

  playPlaylist() {
    this.playTrack(0);
  }

  playTrack(index: number) {
    // Ensure index is within bounds
    index = Math.max(0, Math.min(index, this.music.length - 1));

    // Stop current track if playing
    if (this.currentTrack) {
      this.currentTrack.stop();
    }

    // Get track (lazy load if needed)
    const track = this.getMusicTrack(index);
    if (!track) {
      console.warn(`Failed to load music track at index ${index}`);
      return;
    }

    // Set and play new track
    this.currentTrackIndex = index;
    this.currentTrack = track;

    // Setup end event to play next track
    this.currentTrack.once('end', () => {
      this.nextTrack();
    });

    this.currentTrack.play();
  }

  nextTrack() {
    const nextIndex = (this.currentTrackIndex + 1) % this.music.length;
    this.playTrack(nextIndex);
    this.playSound('uiClick');
  }

  previousTrack() {
    const prevIndex = (this.currentTrackIndex - 1 + this.music.length) % this.music.length;
    this.playTrack(prevIndex);
    this.playSound('uiClick');
  }

  playRandomMusic() {
    if (this.currentTrack) {
      this.currentTrack.stop();
    }
    const randomIndex = Math.floor(Math.random() * this.music.length);
    this.playTrack(randomIndex);
  }

  playSound(soundName: string) {
    const sound = this.sounds.get(soundName);
    if (sound) {
      sound.play();
    }
  }

  // Alias for playSound for better API semantics
  playSoundEffect(soundName: string) {
    this.playSound(soundName);
  }

  stopMusic() {
    if (this.currentTrack) {
      this.currentTrack.stop();
    }
  }

  setMusicVolume(volume: number) {
    this.musicVolume = volume;
    this.music.forEach(track => {
      if (track) {
        track.volume(volume);
      }
    });
  }

  setSFXVolume(volume: number) {
    this.sfxVolume = volume;
    this.sounds.forEach(sound => sound.volume(volume));
  }
}

export const audioManager = new AudioManager();