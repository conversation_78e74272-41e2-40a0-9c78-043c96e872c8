import * as THREE from 'three';
import * as <PERSON><PERSON>N<PERSON> from 'cannon-es';
import { nanoid } from 'nanoid';
import Entity from './Entity.js';
import Specter from './Specter.js';
import Player from './Player.js';
import { SpecterType, SpecterTypeEnum } from '../types.js';
import { audioManager as AudioManager } from '../audio/AudioManager.js';
import GravityEffect from '../effects/GravityEffect.js';

/**
 * Represents equipment that can be attached to a pet specter
 */
export interface SpecterEquipment {
  id: string;
  name: string;
  type: 'weapon' | 'armor' | 'utility';
  level: number;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  stats: {
    attackBonus?: number;
    defenseBonus?: number;
    speedBonus?: number;
    specialEffect?: string;
  };
  model?: THREE.Object3D;
}

/**
 * Specter trait types that can be trained
 */
export enum SpecterTraitType {
  ATTACK = 'attack',
  DEFENSE = 'defense',
  SPEED = 'speed',
  INTELLIGENCE = 'intelligence',
  LOYALTY = 'loyalty'
}

/**
 * Pet specter trait with levels
 */
export interface SpecterTrait {
  type: SpecterTraitType;
  level: number;
  xp: number;
  xpToNextLevel: number;
}

/**
 * Pet Specter that follows the player and assists in combat
 */
export class PetSpecter extends Entity {
  // Basic properties
  public id: string;
  public gameId?: string; // Database gameId for API calls
  public name: string;
  public ownerID: string;
  public specterType: SpecterType;
  public level: number = 1;
  public xp: number = 0;
  public xpToNextLevel: number = 100;
  public tokenId?: string; // NFT token ID if minted
  public metadata: any = {}; // Metadata from database

  // Stats
  public health: number = 100;
  public maxHealth: number = 100;
  public attackPower: number = 10;
  public defenseValue: number = 5;
  public speed: number = 5;

  // Equipment slots
  public equipment: {
    weapon?: SpecterEquipment;
    armor?: SpecterEquipment;
    utility?: SpecterEquipment;
  } = {};

  // Traits that can be trained
  public traits: SpecterTrait[] = [];

  // Combat and behavior
  public isAttacking: boolean = false;
  public currentTarget: THREE.Object3D | null = null;
  public attackRange: number = 550;
  public attackCooldown: number = 0;
  public behaviorState: 'follow' | 'attack' | 'idle' = 'follow';

  // Gravity effects
  private activeGravityEffects: GravityEffect[] = [];

  // Reference to the game engine
  private gameEngine: any = null;

  // Visual and effects
  private glowMesh: THREE.Mesh | null = null;
  private trailParticles: THREE.Points | null = null;
  private trailPositions: Float32Array | null = null;
  private particleCount: number = 20;
  private trailIndex: number = 0;

  // Movement
  private targetPosition: THREE.Vector3 = new THREE.Vector3();
  private followDistance: number = 6; // Increased from 3 to spread pets out more
  private maxDistanceFromOwner: number = 20;
  private returnThreshold: number = 15;
  private floatHeight: number = 1.5; // Height to float above ground
  private floatOffset: number = 0; // For bobbing motion

  // Player reference by ID
  private player: Player | null = null;

  // Custom image URL for AI-generated pet specters
  private customImageUrl?: string;

  // Coordination and collision avoidance
  private avoidanceRadius: number = 12.0; // Distance to maintain from other pets (4x wider)
  private lastTargetScanTime: number = 0;
  private targetScanCooldown: number = 1000; // 1 second between target scans
  private targetReservationTime: number = 3000; // How long to reserve a target

  constructor(
    scene: THREE.Scene,
    world: CANNON.World,
    position: THREE.Vector3,
    ownerID: string,
    specterType: SpecterType,
    name: string = 'Pet Specter',
    customImageUrl?: string,
    gameEngine?: any
  ) {
    super(scene, world);

    this.id = nanoid();
    this.name = name;
    this.ownerID = ownerID;
    this.specterType = specterType;
    this.customImageUrl = customImageUrl;
    this.gameEngine = gameEngine;

    // Initialize traits
    this.traits = [
      { type: SpecterTraitType.ATTACK, level: 1, xp: 0, xpToNextLevel: 100 },
      { type: SpecterTraitType.DEFENSE, level: 1, xp: 0, xpToNextLevel: 100 },
      { type: SpecterTraitType.SPEED, level: 1, xp: 0, xpToNextLevel: 100 },
      { type: SpecterTraitType.INTELLIGENCE, level: 1, xp: 0, xpToNextLevel: 100 },
      { type: SpecterTraitType.LOYALTY, level: 1, xp: 0, xpToNextLevel: 100 }
    ];

    // Create specter mesh
    this.createMesh(position);

    // Create physics body with float height applied in the physics body creation
    this.createPhysicsBody(position);

    // Create visual effects
    this.createVisualEffects();

    // Force initial height to be correct
    if (this.body) {
      // Apply immediate upward force to start floating
      this.body.applyForce(
        new CANNON.Vec3(0, 50 * this.body.mass, 0),
        this.body.position
      );
    }
  }

  /**
   * Create the visual representation of the specter based on its type
   */
  private createMesh(position: THREE.Vector3): void {
    // Create a new group to hold all specter meshes
    this.mesh = new THREE.Group();
    this.mesh.position.copy(position);

    // Always create default mesh first to ensure something is visible immediately
    this.createDefaultMesh();

    // If we have a custom image URL, load it
    if (this.customImageUrl) {
      console.log(`[PetSpecter] Creating mesh with custom image URL: ${this.customImageUrl}`);

      // Store a reference to the main body mesh for later replacement
      let mainBodyMesh: THREE.Mesh | null = null;
      this.mesh.traverse((child) => {
        if (child instanceof THREE.Mesh && child.userData.isMainBody) {
          mainBodyMesh = child;
        }
      });

      // Determine if it's a static asset or a dynamic URL
      const isStaticAsset = this.customImageUrl.startsWith('/assets/');

      const loadTextureAndApply = (url: string) => {
        const textureLoader = new THREE.TextureLoader();
        textureLoader.load(
          url,
          (texture) => {
            console.log(`[PetSpecter] Texture loaded successfully from: ${url}`);

            texture.minFilter = THREE.NearestFilter;
            texture.magFilter = THREE.NearestFilter;
            texture.needsUpdate = true;

            const spriteMaterial = new THREE.SpriteMaterial({
              map: texture,
              color: 0xffffff,
              transparent: true,
              opacity: 1.0
            });

            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(1.5, 1.5, 1);
            sprite.userData.isMainBody = true;
            sprite.userData.hasCustomTexture = true;

            if (mainBodyMesh && this.mesh) {
              console.log('[PetSpecter] Removing placeholder mesh and adding sprite with texture');
              this.mesh.remove(mainBodyMesh);
            }

            if (this.mesh) {
              this.mesh.add(sprite);
              this.createSpriteGlowEffect();

              if (this.id && !isStaticAsset) { // Only store dynamic URLs in localStorage
                const storageKey = this.tokenId ?
                  `nft_pet_image_${this.tokenId}` :
                  `pet_image_${this.id}`;
                console.log(`[PetSpecter] Storing successful texture URL in localStorage with key: ${storageKey}`);
                if (this.customImageUrl) { // Ensure customImageUrl is not undefined
                  localStorage.setItem(storageKey, this.customImageUrl);
                }
              }
            }
          },
          undefined, // Progress callback (not needed for simple load)
          (error) => {
            console.error(`[PetSpecter] Error loading texture from ${url}:`, error);
            // Fallback to default mesh if custom texture fails
          }
        );
      };

      if (isStaticAsset) {
        console.log(`[PetSpecter] Loading static asset texture from: ${this.customImageUrl}`);
        loadTextureAndApply(this.customImageUrl);
      } else {
        // Existing logic for dynamic URLs with retry
        const loadingManager = new THREE.LoadingManager();
        let loadAttempts = 0;
        const maxLoadAttempts = 3;

        loadingManager.onError = (url) => {
          console.error(`[PetSpecter] Error loading dynamic texture from ${url}, attempt ${loadAttempts + 1}/${maxLoadAttempts}`);
          if (loadAttempts < maxLoadAttempts) {
            loadAttempts++;
            console.log(`[PetSpecter] Retrying dynamic texture load (attempt ${loadAttempts}/${maxLoadAttempts})...`);
            setTimeout(() => {
              if (this.mesh) {
                loadDynamicTextureWithRetry();
              }
            }, 1000);
          } else {
            console.error(`[PetSpecter] Failed to load dynamic texture after ${maxLoadAttempts} attempts`);
          }
        };

        const dynamicTextureLoader = new THREE.TextureLoader(loadingManager);
        console.log(`[PetSpecter] Loading dynamic pet texture from: ${this.customImageUrl}`);

        const loadDynamicTextureWithRetry = () => {
          const cacheBuster = `?t=${Date.now()}`;
          const imageUrl = this.customImageUrl || '';
          const urlWithCacheBuster = imageUrl + (imageUrl.includes('?') ? '&cb=' + cacheBuster : cacheBuster);

          console.log(`[PetSpecter] Loading dynamic texture with cache buster: ${urlWithCacheBuster}`);

          dynamicTextureLoader.load(
            urlWithCacheBuster,
            (texture) => {
              console.log('[PetSpecter] Dynamic pet texture loaded successfully');
              texture.minFilter = THREE.NearestFilter;
              texture.magFilter = THREE.NearestFilter;
              texture.needsUpdate = true;

              const spriteMaterial = new THREE.SpriteMaterial({
                map: texture,
                color: 0xffffff,
                transparent: true,
                opacity: 1.0
              });

              const sprite = new THREE.Sprite(spriteMaterial);
              sprite.scale.set(1.5, 1.5, 1);
              sprite.userData.isMainBody = true;
              sprite.userData.hasCustomTexture = true;

              if (mainBodyMesh && this.mesh) {
                console.log('[PetSpecter] Removing placeholder mesh and adding sprite with texture');
                this.mesh.remove(mainBodyMesh);
              }

              if (this.mesh) {
                this.mesh.add(sprite);
                this.createSpriteGlowEffect();
                if (this.id) {
                  const storageKey = this.tokenId ?
                    `nft_pet_image_${this.tokenId}` :
                    `pet_image_${this.id}`;
                  console.log(`[PetSpecter] Storing successful texture URL in localStorage with key: ${storageKey}`);
                  if (this.customImageUrl) { // Ensure customImageUrl is not undefined
                    localStorage.setItem(storageKey, this.customImageUrl);
                  }
                }
              }
            },
            (progress) => {
              if (progress.lengthComputable) {
                const percentComplete = (progress.loaded / progress.total) * 100;
                console.log(`[PetSpecter] Dynamic pet texture loading: ${percentComplete.toFixed(2)}%`);
              }
            }
          );
        };
        loadDynamicTextureWithRetry();
      }
    }

    // Add mesh to scene
    this.scene.add(this.mesh);
  }

  /**
   * Create a glow effect for the sprite-based pet
   */
  private createSpriteGlowEffect(): void {
    if (!this.mesh) return;

    // Get color based on type but make it glow
    const color = this.getColorForType();

    // Create a point light for the glow effect
    const glowLight = new THREE.PointLight(color, 1.0, 3.0);
    glowLight.position.set(0, 0, 0);
    this.mesh.add(glowLight);

    // Create a particle system for additional glow effect
    const particleCount = 20;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);
    const particleColors = new Float32Array(particleCount * 3);

    // Convert hex color to RGB components
    const r = (color >> 16 & 255) / 255;
    const g = (color >> 8 & 255) / 255;
    const b = (color & 255) / 255;

    // Create particles in a sphere around the sprite
    for (let i = 0; i < particleCount; i++) {
      const radius = 0.8 + Math.random() * 0.4;
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.random() * Math.PI * 2;

      particlePositions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
      particlePositions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
      particlePositions[i * 3 + 2] = radius * Math.cos(phi);

      // Set color with slight variation
      particleColors[i * 3] = r * (0.8 + Math.random() * 0.2);
      particleColors[i * 3 + 1] = g * (0.8 + Math.random() * 0.2);
      particleColors[i * 3 + 2] = b * (0.8 + Math.random() * 0.2);
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));

    const particleMaterial = new THREE.PointsMaterial({
      size: 0.1,
      vertexColors: true,
      transparent: true,
      opacity: 0.6,
      blending: THREE.AdditiveBlending
    });

    const particleSystem = new THREE.Points(particleGeometry, particleMaterial);
    this.mesh.add(particleSystem);

    // Animate particles
    const animateParticles = () => {
      if (!this.mesh || !this.mesh.parent) return;

      const positions = particleGeometry.attributes.position.array;
      for (let i = 0; i < particleCount * 3; i += 3) {
        // Gentle pulsating movement
        const time = Date.now() * 0.001;
        const offset = i / 3 * 0.5;
        positions[i] += Math.sin(time + offset) * 0.003;
        positions[i + 1] += Math.cos(time + offset) * 0.003;
        positions[i + 2] += Math.sin(time * 0.7 + offset) * 0.003;

        // Keep particles within bounds
        const x = positions[i];
        const y = positions[i + 1];
        const z = positions[i + 2];
        const distance = Math.sqrt(x * x + y * y + z * z);
        if (distance > 1.5) {
          positions[i] *= 1.4 / distance;
          positions[i + 1] *= 1.4 / distance;
          positions[i + 2] *= 1.4 / distance;
        }
      }
      particleGeometry.attributes.position.needsUpdate = true;

      requestAnimationFrame(animateParticles);
    };

    animateParticles();
  }

  /**
   * Create the default mesh for the pet specter
   */
  private createDefaultMesh(): void {
    if (!this.mesh) return;

    // Properties for the main body based on type
    let mainBodyGeometry: THREE.BufferGeometry;
    let mainBodyMaterial: THREE.Material;

    // Different mesh shapes based on specter type
    switch (this.specterType.name) {
      case 'BANSHEE':
        mainBodyGeometry = new THREE.SphereGeometry(0.4, 16, 16);
        mainBodyMaterial = new THREE.MeshStandardMaterial({
          color: 0xadd8e6,
          emissive: 0x6495ed,
          emissiveIntensity: 0.5,
          transparent: true,
          opacity: 0.8
        });
        break;
      // ... other cases ...
      default:
        mainBodyGeometry = new THREE.SphereGeometry(0.4, 16, 16);
        mainBodyMaterial = new THREE.MeshStandardMaterial({
          color: 0xadd8e6,
          emissive: 0x6495ed,
          emissiveIntensity: 0.5,
          transparent: true,
          opacity: 0.8
        });
    }

    // Create the main body
    const mainBodyMesh = new THREE.Mesh(mainBodyGeometry, mainBodyMaterial);
    mainBodyMesh.userData.isMainBody = true;
    this.mesh.add(mainBodyMesh);

    // Create a new geometry for the glow instead of trying to clone
    // This is safer than accessing potentially empty geometry
    const glowGeometry = new THREE.SphereGeometry(0.6, 16, 16);

    // Get color based on type but make it glow
    const color = this.getColorForType();

    // Create glow material
    const glowMaterial = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.4,
      side: THREE.BackSide
    });

    // Create glow mesh
    const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
    this.mesh.add(glowMesh);
  }

  /**
   * Create physics body for the pet specter
   * @param position Initial position
   */
  private createPhysicsBody(position: THREE.Vector3): void {
    // Create a sphere physics body for the specter
    const radius = 0.5;
    const shape = new CANNON.Sphere(radius);

    // Create the body with collision filtering to prevent pet-pet collisions
    this.body = new CANNON.Body({
      mass: 0.1, // Very light mass
      position: new CANNON.Vec3(position.x, position.y, position.z),
      shape: shape,
      material: this.world.defaultMaterial,
      linearDamping: 0.9, // Higher damping for smoother movement
      fixedRotation: true, // Don't rotate the specter
      collisionFilterGroup: 2, // Pet specter group (different from default)
      collisionFilterMask: 1, // Only collide with solid objects (group 1), not other pets (group 2)
      type: CANNON.BODY_TYPES.DYNAMIC // Ensure it's a dynamic body
    });

    // Add to physics world
    this.world.addBody(this.body);
  }

  /**
   * Create visual effects for the pet specter
   */
  private createVisualEffects(): void {
    // Create glow effect
    this.createGlowEffect();

    // Create trail particles
    this.createTrailParticles();
  }

  /**
   * Create glow effect around the specter
   */
  private createGlowEffect(): void {
    if (!this.mesh) return;

    // Find the main body mesh in the group
    let mainBodyMesh: THREE.Mesh | null = null;

    this.mesh.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.userData && child.userData.isMainBody) {
          mainBodyMesh = child;
        } else if (!mainBodyMesh) {
          // Use first found mesh if none marked as main body
          mainBodyMesh = child;
        }
      }
    });

    if (!mainBodyMesh) {
      // No suitable mesh found, create a default geometry
      const geometry = new THREE.SphereGeometry(0.6, 16, 16);

      // Get color based on type but make it glow
      const color = this.getColorForType();

      // Glow material
      const material = new THREE.MeshBasicMaterial({
        color,
        transparent: true,
        opacity: 0.4,
        side: THREE.BackSide
      });

      // Create glow mesh
      this.glowMesh = new THREE.Mesh(geometry, material);

      if (this.mesh) {
        this.glowMesh.position.copy(this.mesh.position);
        this.scene.add(this.glowMesh);
      }

      return;
    }

    // Clone geometry for glow
    const geometry = new THREE.SphereGeometry(0.6, 16, 16);

    // Get color based on type but make it glow
    const color = this.getColorForType();

    // Glow material
    const material = new THREE.MeshBasicMaterial({
      color,
      transparent: true,
      opacity: 0.4,
      side: THREE.BackSide
    });

    // Create glow mesh
    this.glowMesh = new THREE.Mesh(geometry, material);
    this.glowMesh.scale.multiplyScalar(1.5);
    this.glowMesh.position.copy(this.mesh.position);

    this.scene.add(this.glowMesh);
  }

  /**
   * Get color based on specter type
   */
  private getColorForType(): number {
    // Safe default color if type is not defined
    if (!this.specterType || !this.specterType.name) {
      return 0x3399ff; // Default blue
    }

    // Get the type name from specter type object
    const typeName = this.specterType.name.toUpperCase();

    switch (typeName) {
      case SpecterTypeEnum.WISP: return 0x3399ff; // Blue
      case SpecterTypeEnum.PHANTOM: return 0x33ff66; // Green
      case SpecterTypeEnum.POLTERGEIST: return 0xff9933; // Orange
      case SpecterTypeEnum.WRAITH: return 0x9933ff; // Purple
      case SpecterTypeEnum.BANSHEE: return 0xff3366; // Red
      case "ORANGEPET": return 0xFF6600; // Orange for OrangePet
      default: return 0x3399ff; // Default blue
    }
  }

  /**
   * Create trail particles that follow behind
   */
  private createTrailParticles(): void {
    // Create buffer for particle positions
    this.trailPositions = new Float32Array(this.particleCount * 3);

    // Fill with initial positions
    if (this.mesh) {
      const pos = this.mesh.position;
      for (let i = 0; i < this.particleCount; i++) {
        this.trailPositions[i * 3] = pos.x;
        this.trailPositions[i * 3 + 1] = pos.y;
        this.trailPositions[i * 3 + 2] = pos.z;
      }
    }

    // Create geometry with positions
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(this.trailPositions, 3));

    // Get color based on type but more transparent
    const color = this.getColorForType();

    // Create point material
    const material = new THREE.PointsMaterial({
      color,
      size: 0.15,
      transparent: true,
      opacity: 0.6,
      sizeAttenuation: true
    });

    // Create points
    this.trailParticles = new THREE.Points(geometry, material);
    this.scene.add(this.trailParticles);
  }

  /**
   * Update pet specter
   */
  update(delta: number): void {
    // Ensure we have a player reference
    this.findPlayerInScene();

    if (!this.mesh || !this.body) return;

    // Animate floating motion
    this.floatOffset = Math.sin(Date.now() * 0.001 * 2) * 0.1;

    // Calculate target height based on player position and float parameters
    const targetHeight = this.player ?
      this.player.getPosition().y + this.floatHeight + this.floatOffset :
      this.body.position.y + this.floatHeight + this.floatOffset;

    // Apply floating force to counter gravity (6x stronger for higher gravity)
    this.body.applyForce(
      new CANNON.Vec3(0, 9.8 * 6 * this.body.mass, 0), // Counter gravity with 6x force
      this.body.position
    );

    // If pet is too low, apply extra upward force to correct
    if (this.body.position.y < targetHeight - 0.5) {
      const correctionForce = 9.8 * 8 * this.body.mass * (targetHeight - this.body.position.y);
      this.body.applyForce(
        new CANNON.Vec3(0, correctionForce, 0),
        this.body.position
      );
    }

    // Limit maximum vertical velocity to prevent bouncing
    if (Math.abs(this.body.velocity.y) > 5) {
      this.body.velocity.y *= 0.9;
    }

    // Update mesh visual position
    // For horizontal movement, follow physics precisely
    this.mesh.position.x = this.body.position.x;
    this.mesh.position.z = this.body.position.z;

    // For vertical position, use smoother interpolation for better visual effect
    const currentY = this.mesh.position.y;
    this.mesh.position.y = currentY + (this.body.position.y - currentY) * 0.1;

    // Ensure minimum height
    if (this.mesh.position.y < targetHeight - 1) {
      this.mesh.position.y = targetHeight - 1;
    }

    // Decrease attack cooldown if it's active
    if (this.attackCooldown > 0) {
      this.attackCooldown -= delta;
    }

    // FIXED: Only scan for enemies if in attack mode
    if (this.behaviorState === 'attack' && Math.random() < 0.2) {
      this.scanForEnemies();
    }

    // Handle behavior based on state
    switch (this.behaviorState) {
      case 'follow':
        if (this.player) {
          this.followOwner(delta);
        }
        break;
      case 'attack':
        this.attackEnemies(delta);
        break;
      case 'idle':
        this.idleFloat(delta);
        break;
    }

    // Animate specter components
    this.animateSpecterParts(delta);

    // Update visual effects
    this.updateVisualEffects(delta);

    // Update gravity effects
    this.updateGravityEffects(delta);
  }

  /**
   * Find and store reference to player in scene
   */
  private findPlayerInScene(): void {
    if (this.player) return; // Already found player

    // Search the entire scene for the player
    this.scene.traverse((object) => {
      // Check for player marker objects (created in GameEngine.createPetSpecter)
      if (object.userData && object.userData.isPlayer && object.userData.playerReference) {
        this.player = object.userData.playerReference;
        console.log(`[PetSpecter ${this.name}] Found player in scene`);
        return;
      }

      // Also check for Player instance directly
      if (object.userData && object.userData.entity && object.userData.entity.constructor.name === 'Player') {
        this.player = object.userData.entity;
        console.log(`[PetSpecter ${this.name}] Found player entity in scene`);
        return;
      }
    });

    // If we still can't find the player and we have a gameEngine reference
    if (!this.player && this.gameEngine) {
      try {
        this.player = this.gameEngine.getPlayer();
        if (this.player) {
          console.log(`[PetSpecter ${this.name}] Found player through gameEngine reference`);
        }
      } catch (e) {
        console.error(`[PetSpecter ${this.name}] Error getting player from gameEngine:`, e);
      }
    }

    if (!this.player) {
      console.warn(`[PetSpecter ${this.name}] Could not find player in scene!`);
    }
  }

  /**
   * Follow the owner (player)
   */
  private followOwner(delta: number): void {
    if (!this.body || !this.player || !this.mesh) return;

    // If we're in attack mode, don't follow
    if (this.behaviorState === 'attack' && this.currentTarget) {
      return;
    }

    // Get player position
    const playerPos = this.player.getPosition();

    // Calculate target position behind player based on their velocity
    const playerVelocity = this.player.getVelocity();
    const playerSpeed = new THREE.Vector3(playerVelocity.x, 0, playerVelocity.z).length();

    // Get player's forward direction from camera
    let playerDirection = new THREE.Vector3(0, 0, -1);
    try {
      playerDirection = new THREE.Vector3(0, 0, -1).applyQuaternion(
        new THREE.Quaternion().setFromEuler(new THREE.Euler(0, this.player.getPhysicsBody().quaternion.y, 0))
      );
    } catch (e) {
      // If getting physics body fails, use a default direction
      console.warn("Failed to get player direction:", e);
    }

    // Create unique positioning for each pet based on their ID
    const petIndex = this.gameEngine?.petSpecters?.indexOf(this) || 0;
    const totalPets = this.gameEngine?.petSpecters?.length || 1;

    // Calculate angle offset for this pet to spread them around the player
    const angleOffset = (petIndex / totalPets) * Math.PI * 2;
    const baseRadius = this.followDistance + (petIndex * 2); // Increase radius for each additional pet

    // Position the pet in a formation around the player
    const formationX = Math.cos(angleOffset) * baseRadius;
    const formationZ = Math.sin(angleOffset) * baseRadius;

    this.targetPosition.copy(playerPos)
      .add(new THREE.Vector3(
        formationX + Math.sin(Date.now() * 0.001 + angleOffset) * 1.0, // Side to side movement with unique phase
        this.floatHeight + this.floatOffset, // Float height with bobbing
        formationZ + Math.cos(Date.now() * 0.0015 + angleOffset) * 1.0 // Front to back movement with unique phase
      ));

    // Calculate direction to target
    const currentPos = new THREE.Vector3().copy(this.body.position as unknown as THREE.Vector3);
    const direction = new THREE.Vector3()
      .subVectors(this.targetPosition, currentPos)
      .normalize();

    // Apply force towards target with speed modified by trait level (4x stronger for higher gravity)
    const speedFactor = this.getTraitLevel(SpecterTraitType.SPEED) * 0.5;
    const targetForce = direction.multiplyScalar(this.speed * speedFactor * 4); // 4x force multiplier

    // Apply collision avoidance with other pet specters
    const avoidanceForce = this.calculateAvoidanceForce();

    // Combine target movement with avoidance
    const finalForce = targetForce.add(avoidanceForce.multiplyScalar(1.5));

    this.body.applyForce(
      new CANNON.Vec3(finalForce.x, finalForce.y, finalForce.z),
      new CANNON.Vec3(0, 0, 0)
    );

    // Look at player
    this.mesh.lookAt(playerPos);
  }

  /**
   * Attack enemy specters
   */
  private attackEnemies(delta: number): void {
    if (!this.mesh || !this.body || !this.player) return;

    // Debug: Log the current state
    //console.log(`PetSpecter attack mode: target=${this.currentTarget ? 'exists' : 'none'}`);

    // Apply just enough upward force to maintain float height in attack mode
    // Use a smaller multiplier to prevent flying up
    const floatForce = 9.8 * 1.5 * this.body.mass; // Only 1.5x gravity to maintain height
    this.body.applyForce(
      new CANNON.Vec3(0, floatForce, 0),
      this.body.position
    );

    // Check distance to owner - if too far, start moving toward player while maintaining attack mode
    const playerPos = this.player.getPosition();
    const currentPos = new THREE.Vector3().copy(this.body.position as unknown as THREE.Vector3);
    const distanceToPlayer = currentPos.distanceTo(playerPos);

    if (distanceToPlayer > this.maxDistanceFromOwner) {
      // Too far from player - move back toward player while keeping attack mode
      const direction = new THREE.Vector3()
        .subVectors(playerPos, currentPos)
        .normalize();

      // Apply stronger force to get back to player
      const returnSpeed = this.speed * 2;
      const force = direction.multiplyScalar(returnSpeed);

      this.body.applyForce(
        new CANNON.Vec3(force.x * 4, 0, force.z * 4), // Don't change y force here - it's handled above
        this.body.position
      );

      // Temporarily clear target but stay in attack mode
      this.currentTarget = null;

      // Look at return destination
      this.mesh.lookAt(playerPos);

      // Immediately try to find a new target
      this.scanForEnemies();
      return;
    }

    // Always scan for enemies every few frames to be more aggressive in attack mode
    if (Math.random() < 0.3) {
      this.scanForEnemies();
    }

    // If we have no target or target is invalid, find one
    if (!this.currentTarget || !this.scene.getObjectById(this.currentTarget.id)) {
      this.scanForEnemies();

      // If still no target, stay in attack mode but idle
      if (!this.currentTarget) {
        this.idleFloat(delta);

        // Debug: Log why we couldn't find a target
        //console.log("Pet specter couldn't find any valid targets, staying in attack mode but idling");
        return;
      }
    }

    // If we have a target, move towards it
    if (this.currentTarget) {
      // Check if target is valid
      let targetExists = false;
      let targetPosition = new THREE.Vector3();

      this.scene.traverse((object) => {
        if (object === this.currentTarget) {
          targetExists = true;
          targetPosition.copy(object.position);
        }
      });

      // If target no longer exists, scan for new targets but stay in attack mode
      if (!targetExists) {
        //console.log("Target no longer exists, scanning for new targets");
        this.currentTarget = null;
        this.scanForEnemies();
        return;
      }

      // Calculate direction to target
      const direction = new THREE.Vector3()
        .subVectors(targetPosition, this.mesh.position)
        .normalize();

      // Calculate distance to target
      const distanceToTarget = this.mesh.position.distanceTo(targetPosition);

      // console.log(`Distance to target: ${distanceToTarget}, attack range: ${this.attackRange}`);

      // Look at target
      this.mesh.lookAt(targetPosition);

      // If within attack range and cooldown is complete, attack
      if (distanceToTarget <= this.attackRange && this.attackCooldown <= 0) {
        // console.log("Performing attack!");
        this.performAttack(targetPosition);
      } else {
        // Otherwise move towards target - increase force to move faster
        const baseSpeed = this.speed * 3 + (this.getTraitLevel(SpecterTraitType.SPEED) * 0.5);

        // Apply collision avoidance with other pet specters
        const avoidanceForce = this.calculateAvoidanceForce();
        const targetForce = direction.multiplyScalar(baseSpeed);

        // Combine target movement with avoidance (avoidance has higher priority)
        const finalForce = targetForce.add(avoidanceForce.multiplyScalar(2));

        // Apply horizontal movement force only - vertical force is handled earlier
        this.body.applyForce(
          new CANNON.Vec3(finalForce.x * 4, 0, finalForce.z * 4), // 4x force multiplier for horizontal movement only
          this.body.position
        );

        // Make sure the y velocity isn't too high (prevents excessive bouncing)
        if (Math.abs(this.body.velocity.y) > 5) {
          this.body.velocity.y *= 0.9; // Dampen vertical velocity
        }

        // Debug info - removed excessive logging
        // console.log(`[PetSpecter] Moving toward target: (${this.mesh.position.x.toFixed(2)}, ${this.mesh.position.y.toFixed(2)}, ${this.mesh.position.z.toFixed(2)})`);

        // Ensure the pet is always at the correct height visually
        if (this.mesh && this.body) {
          // Ensure mesh y position is maintained at the desired height
          const targetY = this.player.getPosition().y + this.floatHeight + this.floatOffset;
          const currentY = this.mesh.position.y;
          // Gradually interpolate to the correct height for smooth visual movement
          this.mesh.position.y = currentY + (targetY - currentY) * 0.05;
        }

        // console.log(`Moving toward target at speed ${baseSpeed}, force: ${force.length()}`);
      }
    }
  }

  /**
   * Perform an attack on the target
   */
  private performAttack(targetPosition: THREE.Vector3): void {
    if (!this.mesh) return;

    this.isAttacking = true;

    // Create gravity effect at target position - this is the ONLY attack type
    this.createGravityAttackEffect(targetPosition);

    // Play attack sound
    AudioManager.playSoundEffect('specterAttack');

    // Set cooldown based on attack level (higher level = faster attacks)
    this.attackCooldown = 2.0 - (this.getTraitLevel(SpecterTraitType.ATTACK) * 0.1);
    if (this.attackCooldown < 0.5) this.attackCooldown = 0.5; // Minimum cooldown

    // Look for the target in the scene to apply damage
    this.scene.traverse((object) => {
      if (object === this.currentTarget) {
        // Check if the target has a specter reference
        if (object.userData && object.userData.specterReference) {
          // Cast to any to access properties TypeScript doesn't recognize
          const enemy = object.userData.specterReference as any;

          // Calculate damage based on attack power + equipment
          const attackBonus = this.getEquipmentAttackBonus();
          const baseDamage = this.attackPower + attackBonus;
          const levelBonus = this.getTraitLevel(SpecterTraitType.ATTACK) * 2;
          const damage = baseDamage + levelBonus;

          // Apply damage to enemy if the function exists
          if (typeof enemy.takeDamage === 'function') {
            enemy.takeDamage(damage);

            // Check if enemy defeated
            if (enemy.health !== undefined && enemy.health <= 0) {
              // Gain XP for defeating enemy
              this.gainTraitXP(SpecterTraitType.ATTACK, 10);
              this.gainTraitXP(SpecterTraitType.INTELLIGENCE, 5);

              // Clear target
              this.currentTarget = null;

              // Stay in attack mode - scan for new enemies
              // Don't change behavior state back to follow
            }
          }
        }
      }
    });

    // Reset attack state after a short delay
    setTimeout(() => {
      this.isAttacking = false;
    }, 200);
  }

  /**
   * Scan for enemies within detection range with coordination
   */
  private scanForEnemies(): void {
    if (!this.mesh) return;

    // IMPORTANT: Only scan for enemies if in attack mode
    if (this.behaviorState !== 'attack') {
      return;
    }

    // Implement scan cooldown to prevent excessive scanning
    const currentTime = Date.now();
    if (currentTime - this.lastTargetScanTime < this.targetScanCooldown) {
      return;
    }
    this.lastTargetScanTime = currentTime;

    // Skip if already targeting a valid enemy that's not being targeted by another pet
    if (this.currentTarget && this.scene.getObjectById(this.currentTarget.id)) {
      if (!this.isTargetBeingAttackedByOtherPet(this.currentTarget)) {
        return;
      }
    }

    // Detection range (basically the whole level)
    const detectionRange = 200;

    // Track best target
    let nearestDistance = detectionRange;
    let bestTarget: THREE.Object3D | null = null;

    // For Orange pets, prioritize time-dilated enemies
    if (this.isOrangePet()) {
      bestTarget = this.findTimeDilatedEnemy();
      if (bestTarget) {
        this.currentTarget = bestTarget;
        return;
      }
    }

    // Stats for debugging
    let groupCount = 0;
    let spriteCount = 0;
    let potentialTargets = 0;

    // Search the scene for enemy specters
    this.scene.traverse((object) => {
      // Only consider Groups (since Specters are group objects)
      if (object instanceof THREE.Group) {
        groupCount++;

        // Skip this object if it's our own pet specter
        if (object === this.mesh) return;

        // Check for sprites (enemy specters use sprites)
        let hasSprite = false;
        object.traverse(child => {
          if (child instanceof THREE.Sprite) {
            hasSprite = true;
            spriteCount++;
          }
        });

        // Potential target if it has sprites and userData
        if (hasSprite && object.userData) {
          potentialTargets++;

          // Don't target captured or pet specters
          if (!object.userData.isPetSpecter && !object.userData.isCaptured) {
            // Check if this target is already being attacked by another pet
            if (this.isTargetBeingAttackedByOtherPet(object)) {
              return; // Skip this target
            }

            // For regular pets, avoid time-dilated enemies (leave them for Orange pets)
            if (!this.isOrangePet() && this.isEnemyTimeDilated(object)) {
              return; // Skip time-dilated enemies for regular pets
            }

            // Calculate distance - ensure both mesh and object position are valid
            if (this.mesh && object.position) {
              const distance = this.mesh.position.distanceTo(object.position);

              // If this is closer than our current best, update
              if (distance < nearestDistance) {
                nearestDistance = distance;
                bestTarget = object;
              }
            }
          }
        }
      }
    });

    // Debug
    // console.log(`Scan: ${groupCount} groups, ${spriteCount} with sprites, ${potentialTargets} potential targets, found: ${bestTarget !== null}`);

    // If we found a target, update target but DO NOT change behavior state
    if (bestTarget) {
      this.currentTarget = bestTarget;
      // console.log(`Found target at distance ${nearestDistance}`);

      // Debug marker removed to prevent knockback effect
    }
  }

  /**
   * Check if a target is being attacked by another pet specter
   */
  private isTargetBeingAttackedByOtherPet(target: THREE.Object3D): boolean {
    if (!this.gameEngine || !this.gameEngine.petSpecters) return false;

    for (const otherPet of this.gameEngine.petSpecters) {
      if (otherPet === this) continue; // Skip self
      if (otherPet.currentTarget === target) {
        return true;
      }
    }
    return false;
  }

  /**
   * Find time-dilated enemies for Orange pets to devour
   */
  private findTimeDilatedEnemy(): THREE.Object3D | null {
    if (!this.mesh || !this.gameEngine || !this.gameEngine.specters) return null;

    const devourRange = 15; // Larger search range for time-dilated enemies
    let nearestDistance = devourRange;
    let bestTarget: THREE.Object3D | null = null;

    for (const specter of this.gameEngine.specters) {
      if (specter.timeEffect) { // Enemy is time-dilated
        const distance = this.mesh.position.distanceTo(specter.getPosition());
        if (distance < nearestDistance) {
          // Check if this enemy is already being targeted by another pet
          const enemyMesh = specter.getMesh();
          if (enemyMesh && !this.isTargetBeingAttackedByOtherPet(enemyMesh)) {
            nearestDistance = distance;
            bestTarget = enemyMesh;
          }
        }
      }
    }

    return bestTarget;
  }

  /**
   * Calculate avoidance force to steer away from other pet specters
   */
  private calculateAvoidanceForce(): THREE.Vector3 {
    if (!this.mesh || !this.gameEngine || !this.gameEngine.petSpecters) {
      return new THREE.Vector3(0, 0, 0);
    }

    const avoidanceForce = new THREE.Vector3(0, 0, 0);
    const myPosition = this.mesh.position;

    for (const otherPet of this.gameEngine.petSpecters) {
      if (otherPet === this || !otherPet.mesh) continue; // Skip self and invalid pets

      const otherPosition = otherPet.mesh.position;
      const distance = myPosition.distanceTo(otherPosition);

      // If within avoidance radius, calculate repulsion force
      if (distance < this.avoidanceRadius && distance > 0) {
        const repulsionDirection = new THREE.Vector3()
          .subVectors(myPosition, otherPosition)
          .normalize();

        // Stronger repulsion when closer
        const repulsionStrength = (this.avoidanceRadius - distance) / this.avoidanceRadius;
        const repulsionForce = repulsionDirection.multiplyScalar(repulsionStrength * this.speed);

        avoidanceForce.add(repulsionForce);
      }
    }

    return avoidanceForce;
  }

  /**
   * Check if an enemy is currently time-dilated
   */
  private isEnemyTimeDilated(enemyObject: THREE.Object3D): boolean {
    if (!this.gameEngine || !this.gameEngine.specters) return false;

    // Find the corresponding specter entity for this mesh
    for (const specter of this.gameEngine.specters) {
      if (specter.getMesh() === enemyObject) {
        return !!specter.timeEffect; // Return true if time effect is active
      }
    }
    return false;
  }

  // Removed emitAttackParticle method - pet specter now only uses gravity attacks

  /**
   * Idle floating behavior
   */
  private idleFloat(delta: number): void {
    if (!this.body) return;

    // Simple bobbing motion
    const time = Date.now() * 0.001;

    // Random circular motion
    const radius = 0.5;
    const xForce = Math.sin(time * 0.7) * radius * 0.02;
    const zForce = Math.cos(time * 0.5) * radius * 0.02;

    // Apply gentle forces for idle floating (4x stronger for higher gravity)
    this.body.applyForce(
      new CANNON.Vec3(xForce * 4, Math.sin(time) * 0.2, zForce * 4), // 4x force multiplier
      this.body.position
    );
  }

  /**
   * Update pet specter
   */
  private updateVisualEffects(delta: number): void {
    // Update glow effect
    this.updateGlowEffect(delta);

    // Update trail
    this.updateTrailParticles(delta);
  }

  /**
   * Create a gravity effect at the target position when attacking
   */
  private createGravityAttackEffect(targetPosition: THREE.Vector3): void {
    if (!this.mesh || !this.world) return;

    // Scale gravity effect based on attack level
    const attackLevel = this.getTraitLevel(SpecterTraitType.ATTACK);
    const radius = 3 + (attackLevel * 0.3); // Radius increases with attack level
    const duration = 2.0 + (attackLevel * 0.2); // Duration increases with attack level

    // console.log(`[PetSpecter] Creating gravity effect at ${targetPosition.x.toFixed(2)}, ${targetPosition.y.toFixed(2)}, ${targetPosition.z.toFixed(2)}`);
    // console.log(`[PetSpecter] Gravity effect radius: ${radius.toFixed(2)}, duration: ${duration.toFixed(2)}`);

    // Create gravity effect at target position
    const gravityEffect = new GravityEffect(
      this.scene,
      this.world,
      targetPosition,
      radius,
      duration
    );

    // Make the gravity effect nearly invisible to prevent obscuring enemies
    // This matches how the shattershift rifle's gravity effect works
    if (gravityEffect['effectMesh'] && gravityEffect['effectMesh'].material) {
      const material = gravityEffect['effectMesh'].material as THREE.MeshBasicMaterial;
      const color = this.getColorForType();
      material.color.set(color);
      material.opacity = 0.15; // Very transparent to not obscure enemies

      // Make glow effect nearly invisible too
      gravityEffect['effectMesh'].children.forEach(child => {
        if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshBasicMaterial) {
          child.material.color.set(color);
          child.material.opacity = 0.05; // Almost invisible
        }
      });
    }

    // Create a small visual indicator at the center that doesn't obscure enemies
    const indicatorGeometry = new THREE.SphereGeometry(0.2, 16, 16);
    const color = this.getColorForType();
    const indicatorMaterial = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.7
    });
    const indicatorMesh = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
    indicatorMesh.position.copy(targetPosition);
    this.scene.add(indicatorMesh);

    // Animate the indicator
    let time = 0;
    const animDuration = 0.5;

    const animateIndicator = () => {
      time += 0.016;
      const scale = 1 + Math.sin(time * 10) * 0.2;
      indicatorMesh.scale.set(scale, scale, scale);

      // Fade out
      if (indicatorMaterial.opacity !== undefined) {
        indicatorMaterial.opacity = 0.7 * (1 - time / animDuration);
      }

      // Remove after duration
      if (time >= animDuration) {
        this.scene.remove(indicatorMesh);
        return;
      }

      requestAnimationFrame(animateIndicator);
    };

    animateIndicator();

    // If we have a reference to the game engine, add the effect to its activeEffects array
    // This ensures the effect will be processed by the game engine's update methods
    if (this.gameEngine && typeof this.gameEngine.addActiveEffect === 'function') {
      // console.log(`[PetSpecter] Adding gravity effect to GameEngine's activeEffects array`);
      this.gameEngine.addActiveEffect(
        gravityEffect['effectMesh'],
        'gravity',
        targetPosition,
        radius,
        duration
      );
    } else {
      // Fallback to our own management if game engine is not available
      // console.log(`[PetSpecter] GameEngine not available, adding to local activeGravityEffects array`);
      this.activeGravityEffects.push(gravityEffect);
      // console.log(`[PetSpecter] Added gravity effect to activeGravityEffects array. Total: ${this.activeGravityEffects.length}`);
    }

    // Create particle ring effect to show area of influence without obscuring enemies
    this.createGravityRingEffect(targetPosition, radius, color);

    // Play a sound effect for the gravity attack
    AudioManager.playSoundEffect('specterAttack');
  }

  /**
   * Update active gravity effects and apply them to enemies
   */
  private updateGravityEffects(delta: number): void {
    // Handle devouring for Orange pets
    if (this.isOrangePet()) {
      this.handleDevouring(delta);
    }

    // If we're using the game engine, it will handle applying effects to enemies
    // We only need to manage our own effects if we're not using the game engine
    if (!this.gameEngine || typeof this.gameEngine.addActiveEffect !== 'function') {
      // console.log(`[PetSpecter] Updating ${this.activeGravityEffects.length} active gravity effects`);

      // Update each gravity effect
      for (let i = this.activeGravityEffects.length - 1; i >= 0; i--) {
        const effect = this.activeGravityEffects[i];

        // Update the effect and check if it's still active
        const isActive = effect.update(delta);

        if (!isActive) {
          // Effect has expired, dispose and remove it
          // console.log(`[PetSpecter] Gravity effect expired and removed`);
          effect.dispose();
          this.activeGravityEffects.splice(i, 1);
          continue;
        }

        // Log effect position and radius for debugging
        // console.log(`[PetSpecter] Gravity effect at (${effect.getPosition().x.toFixed(2)}, ${effect.getPosition().y.toFixed(2)}, ${effect.getPosition().z.toFixed(2)}) with radius ${effect.getRadius().toFixed(2)}`);

        // Count how many enemies we find and affect
        let enemiesFound = 0;
        let enemiesAffected = 0;

        // Apply effect to all enemies in the scene
        this.scene.traverse((object) => {
          if (object instanceof THREE.Group && object.userData && object.userData.specterReference) {
            // Skip if it's our own pet specter
            if (object === this.mesh) return;

            // Skip if it's a pet specter or captured specter
            if (object.userData.isPetSpecter || object.userData.isCaptured) return;

            // Found an enemy
            enemiesFound++;

            // Get the enemy reference
            const enemy = object.userData.specterReference as any;

            // IMPORTANT: Call the enemy's applyGravityEffect method directly
            // This is how the shattershift rifle applies gravity effects
            if (enemy && typeof enemy.applyGravityEffect === 'function') {
              // Apply gravity effect directly to the enemy
              enemy.applyGravityEffect(effect.getPosition(), delta);
              enemiesAffected++;

              // Prevent teleporting by resetting the lastTeleportTime
              // This is critical for preventing enemies from teleporting away
              if (enemy.lastTeleportTime !== undefined) {
                enemy.lastTeleportTime = Date.now();
              }
            }
            // Fallback to applying force directly to the physics body
            else if (enemy && enemy.body) {
              effect.applyToBody(enemy.body);
              enemiesAffected++;
            }
          }
          // Also check for DungeonEnemy instances which might not use the specterReference pattern
          else if (object instanceof THREE.Group && object.userData) {
            // Try to find any enemy with a physics body
            const possibleEnemy = object.userData.enemyReference ||
                                object.userData.dungeonEnemyReference ||
                                object.userData.bossReference;

            if (possibleEnemy && possibleEnemy.body) {
              // Found an enemy
              enemiesFound++;

              // Apply gravity effect to the enemy's physics body
              effect.applyToBody(possibleEnemy.body);
              enemiesAffected++;
            }
          }
        });

        // Log how many enemies were found and affected
        // console.log(`[PetSpecter] Found ${enemiesFound} enemies, affected ${enemiesAffected} with gravity`);
      }
    }

    // Always create occasional visual effects to show the pet's gravity field
    // This ensures the player can see the gravity field even if the game engine is handling the effects
    if (Math.random() < 0.02) { // Occasionally create a new ring effect
      // Find a random position near the pet specter for the visual effect
      if (this.mesh) {
        const position = this.mesh.position.clone();
        position.x += (Math.random() - 0.5) * 2;
        position.z += (Math.random() - 0.5) * 2;

        const attackLevel = this.getTraitLevel(SpecterTraitType.ATTACK);
        const radius = 2 + (attackLevel * 0.2);
        const color = this.getColorForType();

        // Create a subtle ring effect to show the pet's gravity field
        this.createGravityRingEffect(position, radius, color);
      }
    }
  }

  /**
   * Create a ring effect to visualize the gravity field without obscuring enemies
   */
  private createGravityRingEffect(position: THREE.Vector3, radius: number, color: number): void {
    // Create a ring of particles around the gravity center
    const particleCount = 20;
    const ringGeometry = new THREE.BufferGeometry();
    const ringPositions = new Float32Array(particleCount * 3);

    // Create particles in a ring
    for (let i = 0; i < particleCount; i++) {
      const angle = (i / particleCount) * Math.PI * 2;
      ringPositions[i * 3] = position.x + Math.cos(angle) * radius;
      ringPositions[i * 3 + 1] = position.y;
      ringPositions[i * 3 + 2] = position.z + Math.sin(angle) * radius;
    }

    ringGeometry.setAttribute('position', new THREE.BufferAttribute(ringPositions, 3));

    // Create material for the ring particles
    const ringMaterial = new THREE.PointsMaterial({
      color: color,
      size: 0.2,
      transparent: true,
      opacity: 0.5
    });

    const ringParticles = new THREE.Points(ringGeometry, ringMaterial);
    this.scene.add(ringParticles);

    // Animate the ring
    let time = 0;
    const duration = 2.0;

    const animateRing = () => {
      time += 0.016;

      // Rotate the ring
      ringParticles.rotation.y += 0.01;

      // Pulse the particles
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;
        const angle = (i / particleCount) * Math.PI * 2 + time;

        // Add a slight pulse effect
        const pulseRadius = radius * (0.95 + Math.sin(angle * 2) * 0.05);

        ringPositions[i3] = position.x + Math.cos(angle) * pulseRadius;
        ringPositions[i3 + 1] = position.y + Math.sin(time * 2 + i * 0.2) * 0.1; // Slight vertical movement
        ringPositions[i3 + 2] = position.z + Math.sin(angle) * pulseRadius;
      }

      ringGeometry.attributes.position.needsUpdate = true;

      // Fade out
      if (ringMaterial.opacity !== undefined) {
        ringMaterial.opacity = 0.5 * (1 - time / duration);
      }

      // Remove after duration
      if (time >= duration) {
        this.scene.remove(ringParticles);
        return;
      }

      requestAnimationFrame(animateRing);
    };

    animateRing();
  }

  /**
   * Update glow effect pulsation
   */
  private updateGlowEffect(delta: number): void {
    if (!this.glowMesh || !this.mesh) return;

    // Update position to match specter
    this.glowMesh.position.copy(this.mesh.position);

    // Pulsate size slightly
    const time = Date.now() * 0.001;
    const scale = 1.5 + Math.sin(time * 2) * 0.1;
    this.glowMesh.scale.set(scale, scale, scale);

    // Update opacity
    if (this.glowMesh.material instanceof THREE.MeshBasicMaterial) {
      this.glowMesh.material.opacity = 0.3 + Math.sin(time * 3) * 0.1;
    }
  }

  /**
   * Update trailing particles
   */
  private updateTrailParticles(delta: number): void {
    if (!this.trailParticles || !this.trailPositions || !this.mesh) return;

    // Add current position to trail at current index
    const i3 = this.trailIndex * 3;
    this.trailPositions[i3] = this.mesh.position.x;
    this.trailPositions[i3 + 1] = this.mesh.position.y;
    this.trailPositions[i3 + 2] = this.mesh.position.z;

    // Update trail index for next frame
    this.trailIndex = (this.trailIndex + 1) % this.particleCount;

    // Update particle system
    this.trailParticles.geometry.attributes.position.needsUpdate = true;

    // Update opacity based on speed
    if (this.trailParticles.material instanceof THREE.PointsMaterial) {
      const speed = this.body ? this.body.velocity.length() : 0;
      this.trailParticles.material.opacity = Math.min(0.6, speed * 0.05);
    }
  }

  /**
   * Animate the specter's parts for more liveliness
   */
  private animateSpecterParts(delta: number): void {
    if (!this.mesh) return;

    const time = Date.now() * 0.001;

    // Animate different parts based on their type
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        // If it's an orbiting wisp
        if (child.userData.orbitSpeed && child.userData.orbitRadius) {
          const angle = time * child.userData.orbitSpeed + child.userData.orbitOffset;
          const radius = child.userData.orbitRadius;

          child.position.x = Math.cos(angle) * radius;
          child.position.y = Math.sin(angle * 0.5) * radius * 0.5 + Math.sin(time * 2) * 0.1;
          child.position.z = Math.sin(angle) * radius;
        }

        // If it's a flow part (for banshee)
        if (child.userData.flowSpeed) {
          child.position.y = -0.3 + Math.sin(time * child.userData.flowSpeed) * 0.1;
          child.rotation.z = Math.sin(time * child.userData.flowSpeed * 0.7) * 0.2;
        }
      }
    });

    // Make the main specter bob gently
    this.floatOffset = Math.sin(time * 1.5) * 0.1;
  }

  /**
   * Equip an item to the specter
   */
  equipItem(item: SpecterEquipment): boolean {
    // Check if slot is available
    if (item.type === 'weapon' && this.equipment.weapon) {
      // Remove old item bonuses
      this.removeEquipmentBonuses(this.equipment.weapon);
    } else if (item.type === 'armor' && this.equipment.armor) {
      this.removeEquipmentBonuses(this.equipment.armor);
    } else if (item.type === 'utility' && this.equipment.utility) {
      this.removeEquipmentBonuses(this.equipment.utility);
    }

    // Equip new item
    if (item.type === 'weapon') {
      this.equipment.weapon = item;
    } else if (item.type === 'armor') {
      this.equipment.armor = item;
    } else if (item.type === 'utility') {
      this.equipment.utility = item;
    }

    // Apply item bonuses
    this.applyEquipmentBonuses(item);

    // Update visuals if there's a model
    if (item.model && this.mesh) {
      // Clone the model to avoid reference issues
      const model = item.model.clone();

      // Add model to specter
      this.mesh.add(model);

      // Position based on equipment type
      if (item.type === 'weapon') {
        model.position.set(0.5, 0, 0);
      } else if (item.type === 'armor') {
        model.position.set(0, 0.2, 0);
      } else {
        model.position.set(0, -0.5, 0);
      }

      // Scale appropriately
      model.scale.set(0.3, 0.3, 0.3);
    }

    // Play equip sound
    AudioManager.playSoundEffect('equipItem');

    return true;
  }

  /**
   * Unequip an item
   */
  unequipItem(type: 'weapon' | 'armor' | 'utility'): SpecterEquipment | null {
    let item: SpecterEquipment | undefined;

    if (type === 'weapon') {
      item = this.equipment.weapon;
      this.equipment.weapon = undefined;
    } else if (type === 'armor') {
      item = this.equipment.armor;
      this.equipment.armor = undefined;
    } else if (type === 'utility') {
      item = this.equipment.utility;
      this.equipment.utility = undefined;
    }

    if (item) {
      // Remove stat bonuses
      this.removeEquipmentBonuses(item);

      // Remove visual model if any
      if (this.mesh) {
        this.mesh.children.forEach(child => {
          if (child.userData && child.userData.equipmentId === item!.id) {
            this.mesh!.remove(child);
          }
        });
      }

      return item;
    }

    return null;
  }

  /**
   * Apply equipment bonuses to specter stats
   */
  private applyEquipmentBonuses(item: SpecterEquipment): void {
    const stats = item.stats;

    if (stats.attackBonus) {
      this.attackPower += stats.attackBonus;
    }

    if (stats.defenseBonus) {
      this.defenseValue += stats.defenseBonus;
    }

    if (stats.speedBonus) {
      this.speed += stats.speedBonus;
    }

    // Special effects could be added here
  }

  /**
   * Remove equipment bonuses from specter stats
   */
  private removeEquipmentBonuses(item: SpecterEquipment): void {
    const stats = item.stats;

    if (stats.attackBonus) {
      this.attackPower -= stats.attackBonus;
    }

    if (stats.defenseBonus) {
      this.defenseValue -= stats.defenseBonus;
    }

    if (stats.speedBonus) {
      this.speed -= stats.speedBonus;
    }

    // Special effects could be removed here
  }

  /**
   * Get total attack bonus from all equipment
   */
  private getEquipmentAttackBonus(): number {
    let bonus = 0;

    if (this.equipment.weapon && this.equipment.weapon.stats.attackBonus) {
      bonus += this.equipment.weapon.stats.attackBonus;
    }

    if (this.equipment.armor && this.equipment.armor.stats.attackBonus) {
      bonus += this.equipment.armor.stats.attackBonus;
    }

    if (this.equipment.utility && this.equipment.utility.stats.attackBonus) {
      bonus += this.equipment.utility.stats.attackBonus;
    }

    return bonus;
  }

  /**
   * Get current level of a trait
   */
  getTraitLevel(traitType: SpecterTraitType): number {
    const trait = this.traits.find(t => t.type === traitType);
    return trait ? trait.level : 1;
  }

  /**
   * Add XP to a specific trait
   */
  gainTraitXP(traitType: SpecterTraitType, amount: number): void {
    const trait = this.traits.find(t => t.type === traitType);

    if (trait) {
      trait.xp += amount;

      // Check for level up
      if (trait.xp >= trait.xpToNextLevel) {
        trait.level += 1;
        trait.xp -= trait.xpToNextLevel;
        trait.xpToNextLevel = Math.floor(trait.xpToNextLevel * 1.5); // Increase XP needed for next level

        // Apply trait level bonuses
        this.applyTraitLevelBonus(traitType, trait.level);

        // Play level up sound
        AudioManager.playSoundEffect('levelUp');

        // Create level up effect
        this.createLevelUpEffect();
      }

      // Save XP progress to database after trait XP gain
      this.saveXPProgress();
    }
  }

  /**
   * Add overall XP to the pet for level progression
   */
  gainXP(amount: number): void {
    this.xp += amount;

    // Check for overall level up
    if (this.xp >= this.xpToNextLevel) {
      this.level += 1;
      this.xp -= this.xpToNextLevel;
      this.xpToNextLevel = Math.floor(this.xpToNextLevel * 1.2); // Increase XP needed for next level

      // Apply overall level bonuses
      this.applyLevelBonus();

      // Play level up sound
      AudioManager.playSoundEffect('levelUp');

      // Create level up effect
      this.createLevelUpEffect();

      console.log(`${this.name} leveled up to level ${this.level}!`);
    }

    // Save XP progress to database
    this.saveXPProgress();
  }

  /**
   * Apply bonuses when the pet's overall level increases
   */
  private applyLevelBonus(): void {
    // Increase base stats on level up
    this.maxHealth += 20;
    this.health += 20;
    this.attackPower += 3;
    this.defenseValue += 2;
    this.speed += 0.3;

    // Increase attack range slightly
    this.attackRange += 10;
  }

  /**
   * Save XP progress to database
   */
  private async saveXPProgress(): Promise<void> {
    try {
      // Create XP data object
      const xpData = {
        level: this.level,
        xp: this.xp,
        xpToNextLevel: this.xpToNextLevel,
        traits: this.traits.map(trait => ({
          type: trait.type,
          level: trait.level,
          xp: trait.xp,
          xpToNextLevel: trait.xpToNextLevel
        }))
      };

      // Dispatch event to save XP progress
      const saveEvent = new CustomEvent('savePetXPProgress', {
        detail: {
          petId: this.id,
          ownerID: this.ownerID,
          xpData: xpData
        }
      });
      window.dispatchEvent(saveEvent);

    } catch (error) {
      console.error('Error saving XP progress:', error);
    }
  }

  /**
   * Apply bonuses when a trait levels up
   */
  private applyTraitLevelBonus(traitType: SpecterTraitType, level: number): void {
    switch (traitType) {
      case SpecterTraitType.ATTACK:
        this.attackPower += 2;
        break;
      case SpecterTraitType.DEFENSE:
        this.defenseValue += 1;
        this.maxHealth += 10;
        this.health += 10;
        break;
      case SpecterTraitType.SPEED:
        this.speed += 0.5;
        break;
      case SpecterTraitType.INTELLIGENCE:
        // Increases detection range in scan method
        break;
      case SpecterTraitType.LOYALTY:
        // Increases follow distance and max range
        this.followDistance += 0.2;
        this.maxDistanceFromOwner += 2;
        this.returnThreshold += 1;
        break;
    }
  }

  /**
   * Create visual effect for level up
   */
  private createLevelUpEffect(): void {
    if (!this.mesh) return;

    // Create ring effect
    const geometry = new THREE.RingGeometry(0.5, 1.5, 32);
    const material = new THREE.MeshBasicMaterial({
      color: 0xffff00,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });

    const ring = new THREE.Mesh(geometry, material);
    ring.position.copy(this.mesh.position);
    ring.rotation.x = Math.PI / 2;
    this.scene.add(ring);

    // Animate ring
    let time = 0;
    const duration = 1.0;

    const animateRing = () => {
      if (!ring.parent) return;

      time += 0.016;

      // Expand ring
      const scale = 1 + time * 3;
      ring.scale.set(scale, scale, scale);

      // Fade out
      if (material.opacity !== undefined) {
        material.opacity = 0.8 * (1 - time / duration);
      }

      // Remove after duration
      if (time >= duration) {
        this.scene.remove(ring);
        return;
      }

      requestAnimationFrame(animateRing);
    };

    animateRing();
  }

  /**
   * Handle taking damage
   */
  takeDamage(amount: number): void {
    // Calculate actual damage based on defense stat
    const defenseFactor = this.defenseValue / (this.defenseValue + 50); // Formula gives diminishing returns
    const actualDamage = amount * (1 - defenseFactor);

    this.health -= actualDamage;

    // Gain defense experience from taking damage
    this.gainTraitXP(SpecterTraitType.DEFENSE, Math.ceil(actualDamage / 5));

    // Check if dead
    if (this.health <= 0) {
      this.die();
    } else {
      // Create hit effect
      this.createHitEffect();
    }
  }

  /**
   * Create visual effect when hit - simplified to not interfere with gravity
   */
  private createHitEffect(): void {
    if (!this.mesh) return;

    // Just flash the specter briefly
    this.mesh.traverse((child) => {
      if (child instanceof THREE.Mesh && child.material instanceof THREE.MeshStandardMaterial) {
        const originalColor = child.material.color.clone();
        child.material.color.set(0xff0000);

        // Reset after a short time
        setTimeout(() => {
          if (child.material instanceof THREE.MeshStandardMaterial) {
            child.material.color.copy(originalColor);
          }
        }, 100);
      }
    });
  }

  /**
   * Handle death
   */
  die(): void {
    // Create death effect
    this.createDeathEffect();

    // Remove from scene
    this.remove();
  }

  /**
   * Create visual effect for death
   */
  private createDeathEffect(): void {
    if (!this.mesh) return;

    // Get position
    const position = this.mesh.position.clone();

    // Create explosion particles
    const particleCount = 50;
    const particleGeometry = new THREE.BufferGeometry();
    const particlePositions = new Float32Array(particleCount * 3);

    // Initialize with position
    for (let i = 0; i < particleCount; i++) {
      particlePositions[i * 3] = position.x;
      particlePositions[i * 3 + 1] = position.y;
      particlePositions[i * 3 + 2] = position.z;
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));

    // Use color based on specter type
    const color = this.getColorForType();
    const particleMaterial = new THREE.PointsMaterial({
      color,
      size: 0.3,
      transparent: true,
      opacity: 0.9
    });

    const particles = new THREE.Points(particleGeometry, particleMaterial);
    this.scene.add(particles);

    // Play death sound
    AudioManager.playSoundEffect('specterDeath');

    // Animate particles outward
    let time = 0;
    const duration = 1.5; // seconds

    const animateExplosion = () => {
      if (!particles.parent) return;

      time += 0.016;

      // Expand particles outward
      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // Calculate 3D direction using spherical coordinates
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI;
        const radius = 5 * time;

        particlePositions[i3] = position.x + radius * Math.sin(phi) * Math.cos(theta);
        particlePositions[i3 + 1] = position.y + radius * Math.sin(phi) * Math.sin(theta);
        particlePositions[i3 + 2] = position.z + radius * Math.cos(phi);
      }

      particles.geometry.attributes.position.needsUpdate = true;

      // Fade out
      if (particleMaterial.opacity !== undefined) {
        particleMaterial.opacity = 0.9 * (1 - time / duration);
      }

      // Remove after duration
      if (time >= duration) {
        this.scene.remove(particles);
        return;
      }

      requestAnimationFrame(animateExplosion);
    };

    animateExplosion();
  }

  /**
   * Remove pet specter from scene
   */
  remove(): void {
    // Remove meshes
    if (this.mesh) {
      this.scene.remove(this.mesh);
      this.mesh = null;
    }

    if (this.glowMesh) {
      this.scene.remove(this.glowMesh);
      this.glowMesh = null;
    }

    if (this.trailParticles) {
      this.scene.remove(this.trailParticles);
      this.trailParticles = null;
    }

    // Clean up any active gravity effects
    this.activeGravityEffects.forEach(effect => {
      effect.dispose();
    });
    this.activeGravityEffects = [];

    // Clear game engine reference
    this.gameEngine = null;

    // Remove physics body
    if (this.body) {
      this.world.removeBody(this.body);
      this.body = null;
    }
  }

  /**
   * Emit a single particle from the specter
   */
  private emitParticle(): void {
    if (!this.mesh) return;

    // Create particle geometry
    const geometry = new THREE.BufferGeometry();
    const vertices = new Float32Array(3);

    // Set initial position at mesh position
    vertices[0] = this.mesh.position.x;
    vertices[1] = this.mesh.position.y;
    vertices[2] = this.mesh.position.z;

    geometry.setAttribute('position', new THREE.BufferAttribute(vertices, 3));

    // Create particle material
    const material = new THREE.PointsMaterial({
      color: this.getColorForType(),
      size: 0.1,
      transparent: true,
      opacity: 0.7,
      sizeAttenuation: true
    });

    // Create particle
    const particle = new THREE.Points(geometry, material);
    this.scene.add(particle);

    // Animate particle
    let time = 0;
    const duration = 0.5 + Math.random() * 0.5; // 0.5-1.0 seconds
    const direction = new THREE.Vector3(
      (Math.random() - 0.5) * 0.5,
      (Math.random() - 0.5) * 0.5,
      (Math.random() - 0.5) * 0.5
    );

    const animateParticle = () => {
      time += 0.016;

      // Move particle
      const pos = particle.geometry.getAttribute('position');
      pos.array[0] += direction.x * 0.01;
      pos.array[1] += direction.y * 0.01;
      pos.array[2] += direction.z * 0.01;
      pos.needsUpdate = true;

      // Fade out
      if (material.opacity !== undefined) {
        material.opacity = 0.7 * (1 - time / duration);
      }

      // Remove after duration
      if (time >= duration) {
        this.scene.remove(particle);
        return;
      }

      requestAnimationFrame(animateParticle);
    };

    animateParticle();
  }

  /**
   * Public method to set the pet specter's behavior state
   * This is the ONLY method that should change behaviorState
   */
  setBehavior(newState: 'follow' | 'attack' | 'idle'): void {
    // Log the behavior change
    //console.log(`PetSpecter: changing behavior from ${this.behaviorState} to ${newState}`);

    // If changing from attack to another state, clear the current target
    if (this.behaviorState === 'attack' && newState !== 'attack') {
      this.currentTarget = null;
    }

    // Set the new behavior state
    this.behaviorState = newState;

    // If switching to attack mode, start scanning for targets
    if (newState === 'attack') {
      this.scanForEnemies();
    }
  }

  /**
   * Sets the position of the pet specter, including both mesh and physics body
   */
  setPosition(position: THREE.Vector3): void {
    if (!this.body || !this.mesh) return;

    // Update physics body position at a proper height
    const positionWithHeight = new THREE.Vector3(
      position.x,
      position.y + this.floatHeight, // Ensure proper height
      position.z
    );

    this.body.position.set(positionWithHeight.x, positionWithHeight.y, positionWithHeight.z);

    // Reset velocity to prevent it from falling too fast
    this.body.velocity.set(0, 0, 0);

    // Wake up the body to ensure physics are applied
    this.body.wakeUp();

    // Update mesh position
    this.mesh.position.copy(positionWithHeight);
  }

  /**
   * Sets the target for the pet specter to follow
   */
  setTarget(target: Player): void {
    this.player = target;
    console.log('Pet specter target set to player');
  }

  /**
   * Check if this is an Orange pet specter
   */
  public isOrangePet(): boolean {
    return this.specterType && (
      this.specterType.name === "OrangePet" ||
      this.specterType.id === 1000 ||
      this.specterType.name.toLowerCase().includes("orange")
    );
  }

  /**
   * Handle devouring mechanism for Orange pets
   */
  private handleDevouring(delta: number): void {
    if (!this.mesh || this.behaviorState !== 'attack') return;

    const devourRange = 2.5; // Closer range for actual devouring
    const timeDilatedEnemies: any[] = [];

    // Check all specters in the game engine
    if (this.gameEngine && this.gameEngine.specters) {
      for (const specter of this.gameEngine.specters) {
        if (specter.timeEffect) {
          const distance = this.mesh.position.distanceTo(specter.getPosition());
          if (distance < devourRange) {
            timeDilatedEnemies.push(specter);
          }
        }
      }
    }

    // Check dungeon enemies if in dungeon
    if (this.gameEngine && this.gameEngine.dungeonManager) {
      const dungeonGenerator = this.gameEngine.dungeonManager.getDungeonGenerator();
      if (dungeonGenerator) {
        const dungeonEnemies = dungeonGenerator.getEnemies();
        for (const enemy of dungeonEnemies) {
          if (enemy.timeEffect) {
            const distance = this.mesh.position.distanceTo(enemy.getPosition());
            if (distance < devourRange) {
              timeDilatedEnemies.push(enemy);
            }
          }
        }
      }
    }

    // Devour the closest time-dilated enemy
    if (timeDilatedEnemies.length > 0) {
      const closestEnemy = timeDilatedEnemies.reduce((closest, enemy) => {
        const closestDist = this.mesh!.position.distanceTo(closest.getPosition());
        const enemyDist = this.mesh!.position.distanceTo(enemy.getPosition());
        return enemyDist < closestDist ? enemy : closest;
      });

      this.devourEnemy(closestEnemy);
    }
  }

  /**
   * Check if an enemy is currently phased
   */
  private isEnemyPhased(enemy: THREE.Object3D): boolean {
    // Check if the enemy has phased visual indicators
    let isPhased = false;

    enemy.traverse((child) => {
      if (child instanceof THREE.Sprite) {
        const material = child.material as THREE.SpriteMaterial;
        // Phased enemies have low opacity and yellow tint
        if (material.opacity < 0.5 && material.color.r > 0.8 && material.color.g > 0.8) {
          isPhased = true;
        }
      }
    });

    return isPhased;
  }

  /**
   * Devour a time-dilated enemy
   */
  private devourEnemy(enemy: any): void {
    if (!this.mesh) return;

    console.log(`Orange pet devouring time-dilated enemy!`);

    // Create devouring particle effect
    this.createDevouringEffect(enemy.getPosition());

    // Dispatch event to GameEngine to handle enemy removal and XP gain
    const devourEvent = new CustomEvent('petDevourEnemy', {
      detail: {
        petId: this.id,
        enemyObject: enemy,
        petPosition: this.mesh.position.clone(),
        enemyPosition: enemy.getPosition().clone()
      }
    });
    window.dispatchEvent(devourEvent);

    // Play devouring sound effect
    AudioManager.playSoundEffect('powerup');
  }

  /**
   * Create visual effect for devouring
   */
  private createDevouringEffect(enemyPosition: THREE.Vector3): void {
    if (!this.mesh) return;

    const particleCount = 25;

    for (let i = 0; i < particleCount; i++) {
      // Create particle sprite
      const particleGeometry = new THREE.PlaneGeometry(0.3, 0.3);
      const particleMaterial = new THREE.MeshBasicMaterial({
        color: 0xff6600, // Orange color
        transparent: true,
        opacity: 0.8,
        blending: THREE.AdditiveBlending
      });

      const particle = new THREE.Mesh(particleGeometry, particleMaterial);

      // Position particle at enemy location with some randomness
      particle.position.copy(enemyPosition);
      particle.position.add(new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2
      ));

      this.scene.add(particle);

      // Animate particle towards pet
      const startPos = particle.position.clone();
      const endPos = this.mesh.position.clone();
      let progress = 0;

      const animateParticle = () => {
        progress += 0.05;

        if (progress >= 1) {
          this.scene.remove(particle);
          particle.material.dispose();
          return;
        }

        // Lerp towards pet position
        particle.position.lerpVectors(startPos, endPos, progress);

        // Fade out as it approaches
        (particle.material as THREE.MeshBasicMaterial).opacity = 0.8 * (1 - progress);

        // Scale down as it approaches
        const scale = 1 - progress * 0.5;
        particle.scale.set(scale, scale, scale);

        requestAnimationFrame(animateParticle);
      };

      animateParticle();
    }
}
public getPosition(): THREE.Vector3 {
    return super.getPosition();
  }

  public dispose(): void {
    super.dispose();
  }
}