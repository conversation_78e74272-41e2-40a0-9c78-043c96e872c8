import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from './AuthContext.js';

interface PvpAccessContextType {
  isPvpEnabled: boolean;
  checkPvpAccess: () => boolean;
}

const PvpAccessContext = createContext<PvpAccessContextType>({
  isPvpEnabled: false,
  checkPvpAccess: () => false,
});

export const usePvpAccess = () => useContext(PvpAccessContext);

interface PvpAccessProviderProps {
  children: React.ReactNode;
}

export const PvpAccessProvider: React.FC<PvpAccessProviderProps> = ({ children }) => {
  const { walletAddress, currentUser, authMethod } = useAuth();
  const [isPvpEnabled, setIsPvpEnabled] = useState<boolean>(false);

  // The specific wallet and email that should have PVP access
  const PVP_ENABLED_WALLET = '******************************************';
  const PVP_ENABLED_EMAIL = '<EMAIL>';

  // Check if the current user has PVP access
  const checkPvpAccess = () => {
    // Early return if no authentication method is available
    if ((!walletAddress && !currentUser?.email) || !currentUser) return false;

    // Only compare the appropriate value based on auth method
    if (authMethod === 'wallet' && walletAddress) {
      // For wallet login, check only the wallet address
      return walletAddress.toLowerCase() === PVP_ENABLED_WALLET.toLowerCase();
    } else if (authMethod === 'orangeID' && currentUser?.email) {
      // For OrangeID login, check only the email
      return currentUser.email.toLowerCase() === PVP_ENABLED_EMAIL.toLowerCase();
    }

    return false;
  };

  // Update PVP access state when authentication changes
  useEffect(() => {
    setIsPvpEnabled(checkPvpAccess());
  }, [walletAddress, currentUser, authMethod]);

  return (
    <PvpAccessContext.Provider value={{ isPvpEnabled, checkPvpAccess }}>
      {children}
    </PvpAccessContext.Provider>
  );
};