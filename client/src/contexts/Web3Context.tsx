import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ethers } from '@/utils/ethersWrapper';
import { useToast } from '@/hooks/use-toast';
import { PetService } from '@/services/petService';

// Define the context type
interface Web3ContextType {
  provider: any | null;
  signer: any | null;
  account: string | null;
  chainId: number | null;
  balance: string | null;
  isConnected: boolean;
  isConnecting: boolean;
  isTestMode: boolean;
  toggleTestMode: () => void;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => void;
  mintNFT: (tokenURI: string, price: string, petData?: any) => Promise<string | null>;
  sendTransaction: (amount: string, data?: string) => Promise<string | null>;
}

// Create the context with default values
const Web3Context = createContext<Web3ContextType>({
  provider: null,
  signer: null,
  account: null,
  chainId: null,
  balance: null,
  isConnected: false,
  isConnecting: false,
  isTestMode: true, // Default to test mode for easier testing
  toggleTestMode: () => {},
  connectWallet: async () => {},
  disconnectWallet: () => {},
  mintNFT: async () => null,
  sendTransaction: async () => null,
});

// NFT Contract ABI (simplified for this example)
const NFT_CONTRACT_ABI = [
  "function mintPetSpecter(string memory tokenURI) public payable returns (uint256)",
  "function ownerOf(uint256 tokenId) public view returns (address)",
  "function tokenURI(uint256 tokenId) public view returns (string memory)"
];

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
console.log('Web3Context initialized in development mode:', isDevelopment);

// Contract addresses - use different addresses based on network
const CONTRACT_ADDRESSES: Record<number, string> = {
  [137]: '******************************************', // Polygon Mainnet
  [80001]: '******************************************', // Mumbai Testnet
  [80002]: '******************************************', // Amoy Testnet
};

// Network constants
const POLYGON_CHAIN_ID = 137; // Polygon Mainnet
const POLYGON_MUMBAI_CHAIN_ID = 80001; // Polygon Mumbai Testnet
const POLYGON_AMOY_CHAIN_ID = 80002; // Polygon Amoy Testnet

// Default to Amoy testnet in development mode
const DEFAULT_CHAIN_ID = isDevelopment ? POLYGON_AMOY_CHAIN_ID : POLYGON_CHAIN_ID;

// Provider props
interface Web3ProviderProps {
  children: ReactNode;
}

// Web3 Provider component
export const Web3Provider: React.FC<Web3ProviderProps> = ({ children }) => {
  const [provider, setProviderState] = useState<any | null>(null);
  const [signer, setSignerState] = useState<any | null>(null);
  const [account, setAccount] = useState<string | null>(null);
  const [chainId, setChainId] = useState<number | null>(null);
  const [balance, setBalance] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [isTestMode, setIsTestMode] = useState<boolean>(true); // Default to test mode

  const { toast } = useToast();

  // Initialize provider from window.ethereum if available
  useEffect(() => {
    const checkIfWalletIsConnected = async () => {
      try {
        // Check if MetaMask is installed
        if (window.ethereum) {
          const accounts = await window.ethereum.request({ method: 'eth_accounts' });

          if (accounts.length > 0) {
            const ethersProvider = new ethers.providers.Web3Provider(window.ethereum);
            const ethersSigner = ethersProvider.getSigner();
            const network = await ethersProvider.getNetwork();
            const accountBalance = await ethersProvider.getBalance(accounts[0]);

            setProviderState(ethersProvider);
            setSignerState(ethersSigner);
            setAccount(accounts[0]);
            setChainId(Number(network.chainId));
            setBalance(ethers.utils.formatEther(accountBalance));
            setIsConnected(true);

            console.log("Wallet is already connected:", accounts[0]);
          }
        } else {
          console.log("Please install MetaMask!");
        }
      } catch (error) {
        console.error("Error checking if wallet is connected:", error);
      }
    };

    checkIfWalletIsConnected();
  }, []);

  // Listen for account changes
  useEffect(() => {
    if (window.ethereum) {
      window.ethereum.on('accountsChanged', (accounts: string[]) => {
        if (accounts.length > 0) {
          setAccount(accounts[0]);
          updateBalance(accounts[0]);
        } else {
          // User disconnected their wallet
          disconnectWallet();
        }
      });

      window.ethereum.on('chainChanged', async (chainIdHex: string) => {
        const newChainId = parseInt(chainIdHex, 16);
        setChainId(newChainId);

        // Log network information for debugging
        try {
          const ethersProvider = new ethers.providers.Web3Provider(window.ethereum);
          const network = await ethersProvider.getNetwork();
          console.log('Network changed:', {
            chainId: newChainId,
            networkName: network.name,
            networkChainId: network.chainId
          });
        } catch (error) {
          console.error('Error getting network details:', error);
        }

        // Check if the chain is Polygon, Mumbai, or Amoy
        if (newChainId !== POLYGON_CHAIN_ID && newChainId !== POLYGON_MUMBAI_CHAIN_ID && newChainId !== POLYGON_AMOY_CHAIN_ID) {
          toast({
            title: "Network Warning",
            description: "Please connect to Polygon network for full functionality",
            variant: "destructive",
            duration: 5000
          });
        }
      });

      return () => {
        window.ethereum.removeAllListeners('accountsChanged');
        window.ethereum.removeAllListeners('chainChanged');
      };
    }
  }, [toast]);

  // Update balance helper function
  const updateBalance = async (address: string) => {
    if (provider) {
      const accountBalance = await provider.getBalance(address);
      setBalance(ethers.utils.formatEther(accountBalance));
    }
  };

  // Connect wallet function
  const connectWallet = async () => {
    setIsConnecting(true);

    try {
      if (window.ethereum) {
        const ethersProvider = new ethers.providers.Web3Provider(window.ethereum);

        // Request account access
        const accounts = await window.ethereum.request({ method: 'eth_requestAccounts' });
        const ethersSigner = ethersProvider.getSigner();
        const network = await ethersProvider.getNetwork();
        const accountBalance = await ethersProvider.getBalance(accounts[0]);

        // Log network information for debugging
        console.log('Connected to network:', {
          chainId: network.chainId,
          networkName: network.name,
          // ensAddress: network.ensAddress // Removed as 'ensAddress' does not exist on type 'Network' in ethers v6
        });

        setProviderState(ethersProvider);
        setSignerState(ethersSigner);
        setAccount(accounts[0]);
        setChainId(Number(network.chainId));
        setBalance(ethers.utils.formatEther(accountBalance));
        setIsConnected(true);

        // Store wallet address in local storage for persistence
        localStorage.setItem('walletAddress', accounts[0]);

        // Check if on Polygon network
        if (Number(network.chainId) !== POLYGON_CHAIN_ID && Number(network.chainId) !== POLYGON_MUMBAI_CHAIN_ID && Number(network.chainId) !== POLYGON_AMOY_CHAIN_ID) {
          toast({
            title: "Network Warning",
            description: "Please switch to Polygon network for full functionality",
            variant: "destructive",
            duration: 5000
          });

          // Optionally, prompt to switch to Polygon Amoy Testnet for development
          try {
            await window.ethereum.request({
              method: 'wallet_switchEthereumChain',
              params: [{ chainId: '0x13882' }], // Polygon Amoy Testnet (80002 in hex)
            });
          } catch (switchError: any) {
            // This error code indicates that the chain has not been added to MetaMask
            if (switchError.code === 4902) {
              try {
                await window.ethereum.request({
                  method: 'wallet_addEthereumChain',
                  params: [
                    {
                      chainId: '0x13882',
                      chainName: 'Polygon Amoy Testnet',
                      nativeCurrency: {
                        name: 'POL',
                        symbol: 'POL',
                        decimals: 18
                      },
                      rpcUrls: ['https://rpc-amoy.polygon.technology/'],
                      blockExplorerUrls: ['https://amoy.polygonscan.com/']
                    }
                  ],
                });
              } catch (addError) {
                console.error("Error adding Polygon network:", addError);
              }
            }
          }
        }

        toast({
          title: "Wallet Connected",
          description: `Connected to ${accounts[0].substring(0, 6)}...${accounts[0].substring(38)}`,
          duration: 3000
        });
      } else {
        toast({
          title: "MetaMask Required",
          description: "Please install MetaMask to connect your wallet",
          variant: "destructive",
          duration: 5000
        });
      }
    } catch (error) {
      console.error("Error connecting wallet:", error);
      toast({
        title: "Connection Failed",
        description: "Failed to connect wallet. Please try again.",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Disconnect wallet function
  const disconnectWallet = () => {
    setProviderState(null);
    setSignerState(null);
    setAccount(null);
    setChainId(null);
    setBalance(null);
    setIsConnected(false);

    // Remove wallet address from local storage
    localStorage.removeItem('walletAddress');

    toast({
      title: "Wallet Disconnected",
      description: "Your wallet has been disconnected",
      duration: 3000
    });
  };

  // Mint NFT function
  const mintNFT = async (tokenURI: string, price: string, petSpecterId?: string): Promise<string | null> => {
    console.log('mintNFT called with:', { tokenURI, price, petSpecterId, isDevelopment, chainId });
    if (!signer || !provider) { // Check state variables
      toast({
        title: "Wallet Not Connected",
        description: "Please connect your wallet to mint an NFT",
        variant: "destructive",
        duration: 3000
      });
      return null;
    }

    // Ensure petSpecterId is in the correct format for later use
    const gameId = petSpecterId && typeof petSpecterId === 'string' ?
      (petSpecterId.startsWith('pet-') ? petSpecterId : `pet-${petSpecterId}`) :
      (petSpecterId ? `pet-${petSpecterId}` : undefined);

    // In development mode or test mode with no contract, use a mock implementation
    const useMockImplementation = (isDevelopment || isTestMode) && (!chainId || !CONTRACT_ADDRESSES[chainId as keyof typeof CONTRACT_ADDRESSES] || CONTRACT_ADDRESSES[chainId as keyof typeof CONTRACT_ADDRESSES] === '******************************************');
    console.log('Using mock NFT implementation?', useMockImplementation, { isDevelopment, isTestMode, chainId, contractAddress: chainId ? CONTRACT_ADDRESSES[chainId as keyof typeof CONTRACT_ADDRESSES] : 'none' });
    if (useMockImplementation) {
      console.log('Using mock NFT minting in development mode');

      try {
        // Generate a mock token ID
        const mockTokenId = `${Date.now()}`;

        // Show a toast to simulate transaction submission
        toast({
          title: isTestMode ? "Test Mode" : "Development Mode",
          description: "Simulating NFT minting transaction...",
          duration: 3000
        });

        // Simulate a delay for the transaction
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Record the mock transaction in the database
        try {
          const mockTxHash = `0x${Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('')}`;

          if (account) { // Ensure account is not null
            await PetService.recordNftTransaction({
              txHash: mockTxHash,
              tokenId: mockTokenId,
              walletAddress: account,
              petSpecterId: petSpecterId && !isNaN(parseInt(petSpecterId.replace('pet-', ''))) ? parseInt(petSpecterId.replace('pet-', '')) : undefined,
              price: price || '0.01',
              platformFee: (parseFloat(price || '0.01') * 0.03).toString(),
              status: 'completed'
            });

            // If we have a pet specter ID, link it to the NFT
            if (petSpecterId && gameId) {
              console.log(`Linking pet with gameId: ${gameId} to NFT: ${mockTokenId}`);
              await PetService.linkPetSpecterToNFT(gameId, mockTokenId, account);
            }
          }
        } catch (dbError) {
          console.error("Error recording mock NFT transaction in database:", dbError);
          // Continue even if database recording fails
        }

        toast({
          title: isTestMode ? "Test Mode" : "Development Mode",
          description: `Mock NFT minted with ID: ${mockTokenId}`,
          duration: 5000
        });

        return mockTokenId;
      } catch (error) {
        console.error("Error in mock NFT minting:", error);

        toast({
          title: "Mock Minting Failed",
          description: "Failed to simulate NFT minting. Please try again.",
          variant: "destructive",
          duration: 5000
        });

        return null;
      }
    }

    // Real implementation for production or when contract is available
    try {
      // Get the contract address for the current network
      const contractAddress = chainId ? CONTRACT_ADDRESSES[chainId as keyof typeof CONTRACT_ADDRESSES] : CONTRACT_ADDRESSES[DEFAULT_CHAIN_ID as keyof typeof CONTRACT_ADDRESSES];

      if (!contractAddress || contractAddress === '******************************************') {
        throw new Error(`No contract address configured for chain ID ${chainId}`);
      }

      const nftContract = new ethers.Contract(
        contractAddress,
        NFT_CONTRACT_ABI,
        signer
      );

      // Convert price to wei
      const priceInWei = ethers.utils.parseEther(price);

      // Calculate platform fee (3%)
      const platformFee = (priceInWei * 3n) / 100n;
      const totalCost = priceInWei + platformFee;

      // Mint the NFT
      const tx = await (nftContract as any).mintPetSpecter(tokenURI, {
        value: totalCost
      });

      toast({
        title: "Transaction Submitted",
        description: "Your NFT minting transaction has been submitted",
        duration: 3000
      });

      // Wait for transaction to be mined
      const receipt = await tx.wait();

      // Find the transfer event to get the token ID
      const transferEvent = receipt.events?.find(
        (event: any) => event.event === 'Transfer'
      );

      if (transferEvent && transferEvent.args) {
        const tokenId = transferEvent.args.tokenId.toString();

        // Record the transaction in the database
        try {
          if (account) { // Ensure account is not null
            await PetService.recordNftTransaction({
              txHash: tx.hash,
              tokenId,
              walletAddress: account,
              petSpecterId: petSpecterId && !isNaN(parseInt(petSpecterId.replace('pet-', ''))) ? parseInt(petSpecterId.replace('pet-', '')) : undefined,
              price,
              platformFee: ethers.utils.formatEther(platformFee),
              status: 'completed'
            });

            // If we have a pet specter ID, link it to the NFT
            if (petSpecterId && gameId) {
              console.log(`Linking pet with gameId: ${gameId} to NFT: ${tokenId}`);
              await PetService.linkPetSpecterToNFT(gameId, tokenId, account);
            }
          }
        } catch (dbError) {
          console.error("Error recording NFT transaction in database:", dbError);
          // Continue even if database recording fails
        }

        toast({
          title: "NFT Minted Successfully",
          description: `Your Pet Specter NFT has been minted with ID: ${tokenId}`,
          duration: 5000
        });

        return tokenId;
      } else {
        console.error("Could not find Transfer event or tokenId in transaction receipt", receipt);
         toast({
            title: "Minting Issue",
            description: "NFT minted, but tokenId could not be retrieved. Please check your wallet.",
            variant: "destructive",
            duration: 5000
          });
        return null;
      }

    } catch (error: any) {
      console.error("Error minting NFT:", error);

      toast({
        title: "Minting Failed",
        description: error.message || "Failed to mint NFT. Please try again.",
        variant: "destructive",
        duration: 5000
      });

      return null;
    }
  };

  // Toggle test mode function - modified to always stay in test mode
  const toggleTestMode = () => {
    // Force test mode to always be true in production
    if (!isTestMode) {
      setIsTestMode(true);
      toast({
        title: "Test Mode Enabled",
        description: "Transactions will be simulated without spending POL",
        duration: 3000
      });
    } else {
      // Just show a message that we're staying in test mode
      toast({
        title: "Remaining in Test Mode",
        description: "Live mode is currently disabled. All transactions will be simulated.",
        duration: 3000
      });
    }
  };

  // Send transaction function
  const sendTransaction = async (amount: string, data?: string): Promise<string | null> => {
    if (!signer || !provider) { // Check state variables
      toast({
        title: "Wallet Not Connected",
        description: "Please connect your wallet to send a transaction",
        variant: "destructive",
        duration: 3000
      });
      return null;
    }

    // In development mode or test mode, use a mock implementation
    if (isDevelopment || isTestMode) {
      console.log('Using mock transaction in development mode');

      try {
        // Show a toast to simulate transaction submission
        toast({
          title: isTestMode ? "Test Mode" : "Development Mode",
          description: "Simulating transaction...",
          duration: 3000
        });

        // Simulate a delay for the transaction
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Generate a mock transaction hash
        const mockTxHash = `0x${Array.from({length: 64}, () => Math.floor(Math.random() * 16).toString(16)).join('')}`;

        // Record the transaction in the database if needed
        // This would depend on what kind of transaction it is

        toast({
          title: isTestMode ? "Test Mode" : "Development Mode",
          description: `Mock transaction sent: ${amount} POL`,
          duration: 5000
        });

        return mockTxHash;
      } catch (error) {
        console.error("Error in mock transaction:", error);

        toast({
          title: "Mock Transaction Failed",
          description: "Failed to simulate transaction. Please try again.",
          variant: "destructive",
          duration: 5000
        });

        return null;
      }
    }

    // Real implementation for production
    try {
      // Convert amount to wei
      const amountInWei = ethers.utils.parseEther(amount);

      // Create transaction object
      const tx = {
        to: process.env.NEXT_PUBLIC_PLATFORM_WALLET || '******************************************',
        value: amountInWei,
        data: data ? ethers.utils.hexlify(ethers.utils.toUtf8Bytes(data)) : '0x',
      };

      // Send transaction
      const transaction = await signer.sendTransaction(tx); // Use state variable 'signer'

      toast({
        title: "Transaction Submitted",
        description: "Your transaction has been submitted",
        duration: 3000
      });

      // Wait for transaction to be mined
      const receipt = await transaction.wait();

      toast({
        title: "Transaction Successful",
        description: `You've sent ${amount} POL successfully`,
        duration: 5000
      });

      return transaction.hash;
    } catch (error: any) {
      console.error("Error sending transaction:", error);

      toast({
        title: "Transaction Failed",
        description: error.message || "Failed to send transaction. Please try again.",
        variant: "destructive",
        duration: 5000
      });

      return null;
    }
  };

  // Context value
  const contextValue: Web3ContextType = {
    provider: provider, // Use state variable
    signer: signer, // Use state variable
    account,
    chainId,
    balance,
    isConnected,
    isConnecting,
    isTestMode,
    toggleTestMode,
    connectWallet,
    disconnectWallet,
    mintNFT,
    sendTransaction
  };

  return (
    <Web3Context.Provider value={contextValue}>
      {children}
    </Web3Context.Provider>
  );
};

// Custom hook to use the Web3 context
export const useWeb3 = () => useContext(Web3Context);

// Add window.ethereum type
declare global {
  interface Window {
    ethereum: any;
  }
}
