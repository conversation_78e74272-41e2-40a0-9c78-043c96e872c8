{"include": ["client/src/**/*", "shared/**/*", "server/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "allowImportingTsExtensions": true, "module": "ESNext", "target": "es2022", "downlevelIteration": true, "strict": true, "lib": ["esnext", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client", "jest", "@testing-library/jest-dom"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"]}}}