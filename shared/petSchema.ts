import { pgTable, text, serial, integer, boolean, timestamp, jsonb, foreignKey } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { users } from "./schema.js";

// Pet Specters schema
export const petSpecters = pgTable("pet_specters", {
  id: serial("id").primaryKey(),
  gameId: text("game_id").notNull(), // In-game ID
  name: text("name").notNull(),
  ownerId: integer("owner_id").references(() => users.id), // Optional reference to user
  walletAddress: text("wallet_address"), // Owner's wallet address
  tokenId: text("token_id"), // NFT token ID if minted
  specterType: text("specter_type").notNull(), // Type name (WISP, PHANTOM, etc.)
  level: integer("level").default(1).notNull(),
  xp: integer("xp").default(0).notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  lastActive: timestamp("last_active").defaultNow(),
  // Store traits as JSON
  traits: jsonb("traits").default([]).notNull(),
  // Store equipment as JSON
  equipment: jsonb("equipment").default({}).notNull(),
  // Store stats as JSON
  stats: jsonb("stats").default({}).notNull(),
  // Store metadata as JSON (for additional properties)
  metadata: jsonb("metadata").default({}).notNull().$type<PetSpecterMetadata>(),
});

export const insertPetSpecterSchema = createInsertSchema(petSpecters).pick({
  gameId: true,
  name: true,
  ownerId: true,
  walletAddress: true,
  tokenId: true,
  specterType: true,
  level: true,
  xp: true,
  traits: true,
  equipment: true,
  stats: true,
  metadata: true,
});

export type InsertPetSpecter = z.infer<typeof insertPetSpecterSchema>;
export type PetSpecter = typeof petSpecters.$inferSelect;

// NFT Transactions schema
export const nftTransactions = pgTable("nft_transactions", {
  id: serial("id").primaryKey(),
  txHash: text("tx_hash").notNull().unique(), // Transaction hash
  tokenId: text("token_id").notNull(), // NFT token ID
  walletAddress: text("wallet_address").notNull(), // Owner's wallet address
  petSpecterId: integer("pet_specter_id").references(() => petSpecters.id),
  price: text("price").notNull(), // Price in MATIC
  platformFee: text("platform_fee").notNull(), // Platform fee in MATIC
  timestamp: timestamp("timestamp").defaultNow(),
  status: text("status").default("completed").notNull(), // "pending", "completed", "failed"
  metadata: jsonb("metadata").default({}).notNull(), // Additional metadata
});

export const insertNftTransactionSchema = createInsertSchema(nftTransactions).pick({
  txHash: true,
  tokenId: true,
  walletAddress: true,
  petSpecterId: true,
  price: true,
  platformFee: true,
  status: true,
  metadata: true,
});

export type InsertNftTransaction = z.infer<typeof insertNftTransactionSchema>;
export type NftTransaction = typeof nftTransactions.$inferSelect;

// Wallet Balances schema
export const walletBalances = pgTable("wallet_balances", {
  id: serial("id").primaryKey(),
  walletAddress: text("wallet_address").notNull().unique(),
  userId: integer("user_id").references(() => users.id),
  maticBalance: text("matic_balance").default("0").notNull(),
  ektoBalance: text("ekto_balance").default("0").notNull(),
  lastUpdated: timestamp("last_updated").defaultNow(),
});

export const insertWalletBalanceSchema = createInsertSchema(walletBalances).pick({
  walletAddress: true,
  userId: true,
  maticBalance: true,
  ektoBalance: true,
});

export type InsertWalletBalance = z.infer<typeof insertWalletBalanceSchema>;
export type WalletBalance = typeof walletBalances.$inferSelect;

// Pet Specter Trait type
export interface PetSpecterTrait {
  type: string; // "ATTACK", "DEFENSE", "SPEED", "INTELLIGENCE", "LOYALTY"
  level: number;
  xp: number;
  xpToNextLevel: number;
}

// Pet Specter Equipment type
export interface PetSpecterEquipment {
  weapon?: {
    id: string;
    name: string;
    attackBonus: number;
    effects: string[];
  };
  armor?: {
    id: string;
    name: string;
    defenseBonus: number;
    effects: string[];
  };
  utility?: {
    id: string;
    name: string;
    effects: string[];
  };
}

// Pet Specter Stats type
export interface PetSpecterStats {
  health: number;
  maxHealth: number;
  attackPower: number;
  defenseValue: number;
  speed: number;
}
// Pet Specter Metadata type
export interface PetSpecterMetadata {
  orangeId?: string;
  level?: number;
  xp?: number;
  xpToNextLevel?: number;
  traits?: PetSpecterTrait[];
  lastXpUpdate?: string;
  [key: string]: any; // Allow for additional properties
}

// Training Transactions schema
export const trainingTransactions = pgTable("training_transactions", {
  id: serial("id").primaryKey(),
  txHash: text("tx_hash").notNull().unique(), // Transaction hash
  walletAddress: text("wallet_address").notNull(), // Owner's wallet address
  petSpecterId: integer("pet_specter_id").references(() => petSpecters.id),
  traitType: text("trait_type").notNull(), // Type of trait being trained
  amount: text("amount").notNull(), // Amount in MATIC
  platformFee: text("platform_fee").notNull(), // Platform fee in MATIC
  timestamp: timestamp("timestamp").defaultNow(),
  status: text("status").default("completed").notNull(), // "pending", "completed", "failed"
  metadata: jsonb("metadata").default({}).notNull(), // Additional metadata
});

export const insertTrainingTransactionSchema = createInsertSchema(trainingTransactions).pick({
  txHash: true,
  walletAddress: true,
  petSpecterId: true,
  traitType: true,
  amount: true,
  platformFee: true,
  status: true,
  metadata: true,
});

export type InsertTrainingTransaction = z.infer<typeof insertTrainingTransactionSchema>;
export type TrainingTransaction = typeof trainingTransactions.$inferSelect;

// PVP Bets schema
export const pvpBets = pgTable("pvp_bets", {
  id: serial("id").primaryKey(),
  txHash: text("tx_hash").notNull().unique(), // Transaction hash
  walletAddress: text("wallet_address").notNull(), // Bettor's wallet address
  matchId: text("match_id").notNull(), // ID of the PVP match
  petSpecterId: integer("pet_specter_id").references(() => petSpecters.id), // Pet being bet on
  amount: text("amount").notNull(), // Bet amount in MATIC
  odds: text("odds").notNull(), // Odds at time of bet
  platformFee: text("platform_fee").notNull(), // Platform fee in MATIC
  potentialWinnings: text("potential_winnings").notNull(), // Potential winnings in MATIC
  outcome: text("outcome").default("pending").notNull(), // "pending", "won", "lost"
  paidOut: boolean("paid_out").default(false).notNull(), // Whether winnings have been paid out
  timestamp: timestamp("timestamp").defaultNow(),
  metadata: jsonb("metadata").default({}).notNull(), // Additional metadata
});

export const insertPvpBetSchema = createInsertSchema(pvpBets).pick({
  txHash: true,
  walletAddress: true,
  matchId: true,
  petSpecterId: true,
  amount: true,
  odds: true,
  platformFee: true,
  potentialWinnings: true,
  outcome: true,
  paidOut: true,
  metadata: true,
});

export type InsertPvpBet = z.infer<typeof insertPvpBetSchema>;
export type PvpBet = typeof pvpBets.$inferSelect;

// PVP Matches schema
export const pvpMatches = pgTable("pvp_matches", {
  id: serial("id").primaryKey(),
  matchId: text("match_id").notNull().unique(), // Unique match identifier
  player1WalletAddress: text("player1_wallet_address").notNull(),
  player2WalletAddress: text("player2_wallet_address").notNull(),
  player1PetId: integer("player1_pet_id").references(() => petSpecters.id),
  player2PetId: integer("player2_pet_id").references(() => petSpecters.id),
  winnerId: integer("winner_id").references(() => petSpecters.id), // ID of winning pet
  totalBetAmount: text("total_bet_amount").default("0").notNull(), // Total amount bet on this match
  totalPlatformFee: text("total_platform_fee").default("0").notNull(), // Total platform fees collected
  prizeFeeContribution: text("prize_fee_contribution").default("0").notNull(), // Amount contributed to tournament prize pool
  status: text("status").default("scheduled").notNull(), // "scheduled", "in_progress", "completed", "cancelled"
  startTime: timestamp("start_time"),
  endTime: timestamp("end_time"),
  createdAt: timestamp("created_at").defaultNow(),
  metadata: jsonb("metadata").default({}).notNull(), // Additional metadata
});

export const insertPvpMatchSchema = createInsertSchema(pvpMatches).pick({
  matchId: true,
  player1WalletAddress: true,
  player2WalletAddress: true,
  player1PetId: true,
  player2PetId: true,
  winnerId: true,
  totalBetAmount: true,
  totalPlatformFee: true,
  prizeFeeContribution: true,
  status: true,
  startTime: true,
  endTime: true,
  metadata: true,
});

export type InsertPvpMatch = z.infer<typeof insertPvpMatchSchema>;
export type PvpMatch = typeof pvpMatches.$inferSelect;

// Tournaments schema
export const tournaments = pgTable("tournaments", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  entryFee: text("entry_fee").notNull(), // Entry fee in MATIC
  prizePool: text("prize_pool").default("0").notNull(), // Total prize pool in MATIC
  maxParticipants: integer("max_participants").default(16).notNull(),
  currentParticipants: integer("current_participants").default(0).notNull(),
  status: text("status").default("registration").notNull(), // "registration", "in_progress", "completed", "cancelled"
  startTime: timestamp("start_time"),
  endTime: timestamp("end_time"),
  createdAt: timestamp("created_at").defaultNow(),
  metadata: jsonb("metadata").default({}).notNull(), // Additional metadata (rules, format, etc.)
});

export const insertTournamentSchema = createInsertSchema(tournaments).pick({
  name: true,
  description: true,
  entryFee: true,
  prizePool: true,
  maxParticipants: true,
  currentParticipants: true,
  status: true,
  startTime: true,
  endTime: true,
  metadata: true,
});

export type InsertTournament = z.infer<typeof insertTournamentSchema>;
export type Tournament = typeof tournaments.$inferSelect;

// Tournament Participants schema
export const tournamentParticipants = pgTable("tournament_participants", {
  id: serial("id").primaryKey(),
  tournamentId: integer("tournament_id").references(() => tournaments.id).notNull(),
  walletAddress: text("wallet_address").notNull(),
  petSpecterId: integer("pet_specter_id").references(() => petSpecters.id).notNull(),
  entryTxHash: text("entry_tx_hash"), // Transaction hash for entry fee
  rank: integer("rank"), // Final ranking in tournament
  prize: text("prize"), // Prize amount in MATIC
  prizeTxHash: text("prize_tx_hash"), // Transaction hash for prize payout
  registeredAt: timestamp("registered_at").defaultNow(),
  metadata: jsonb("metadata").default({}).notNull(), // Additional metadata
});

export const insertTournamentParticipantSchema = createInsertSchema(tournamentParticipants).pick({
  tournamentId: true,
  walletAddress: true,
  petSpecterId: true,
  entryTxHash: true,
  rank: true,
  prize: true,
  prizeTxHash: true,
  metadata: true,
});

export type InsertTournamentParticipant = z.infer<typeof insertTournamentParticipantSchema>;
export type TournamentParticipant = typeof tournamentParticipants.$inferSelect;

