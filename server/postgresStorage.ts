import { eq, desc, and, sql } from 'drizzle-orm';
import { db } from './db.js';
import {
  users,
  highScores,
  gameArenas,
  gamePlayers,
  gameTeams,
  type User,
  type InsertUser,
  type HighScore,
  type InsertHighScore,
  type GameArena,
  type InsertGameArena,
  type GamePlayer,
  type InsertGamePlayer,
  type GameTeam,
  type InsertGameTeam,
  GameMode
} from "@shared/schema.js";
import { IStorage } from './storage.js';

export class PostgresStorage implements IStorage {

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id));
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username));
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await db.insert(users).values(insertUser).returning();
    return result[0];
  }

  // High Score methods
  async getHighScores(gameMode?: GameMode, limit: number = 10): Promise<HighScore[]> {
    if (gameMode) {
      return await db.select()
        .from(highScores)
        .where(eq(highScores.gameMode, gameMode))
        .orderBy(desc(highScores.score))
        .limit(limit);
    } else {
      return await db.select()
        .from(highScores)
        .orderBy(desc(highScores.score))
        .limit(limit);
    }
  }

  async saveHighScore(playerName: string, score: number, gameMode: GameMode = GameMode.SinglePlayer, teamName?: string): Promise<HighScore> {
    const result = await db.insert(highScores)
      .values({
        playerName,
        score,
        gameMode,
        teamName
      })
      .returning();
    return result[0];
  }

  // Game Arena methods
  async createGameArena(arena: InsertGameArena): Promise<GameArena> {
    const now = new Date();

    const gameArena = {
      name: arena.name,
      mode: arena.mode,
      maxPlayers: arena.maxPlayers || 4,
      createdAt: now,
      startedAt: null,
      endedAt: null,
      currentLevel: 1,
      enemiesTotal: 10, // Start with 10 enemies as specified
      enemiesDefeated: 0,
      status: 'waiting',
      settings: arena.settings || {}
    };

    const result = await db.insert(gameArenas)
      .values(gameArena)
      .returning();

    console.log(`Created game arena with ID=${result[0].id}, name=${arena.name}, mode=${arena.mode}`);
    return result[0];
  }

  async getGameArena(id: number): Promise<GameArena | undefined> {
    console.log(`[Storage] Getting arena with ID: ${id} (type=${typeof id})`);

    // Ensure the ID is a number
    const numericId = parseInt(String(id), 10);
    console.log(`[Storage] Using numeric ID: ${numericId} (original: ${id})`);

    const result = await db.select()
      .from(gameArenas)
      .where(eq(gameArenas.id, numericId));

    const arena = result[0];
    console.log(`[Storage] Arena ${numericId} found:`, arena ? 'Yes' : 'No');

    if (arena) {
      console.log(`[Storage] Arena details:`, {
        id: arena.id,
        name: arena.name,
        mode: arena.mode,
        status: arena.status
      });
    } else {
      console.error(`[Storage] Arena ${numericId} not found!`);
    }

    return arena;
  }

  async getAllGameArenas(status?: string): Promise<GameArena[]> {
    console.log(`[Storage] Getting all arenas${status ? ` with status: ${status}` : ''}`);

    let result;
    if (status) {
      result = await db.select()
        .from(gameArenas)
        .where(eq(gameArenas.status, status));
    } else {
      result = await db.select().from(gameArenas);
    }

    console.log(`[Storage] Found ${result.length} arenas`);

    // Log all arena IDs for debugging
    console.log(`[Storage] Arena IDs:`, result.map(a => a.id));

    return result;
  }

  async updateGameArena(id: number, updates: Partial<GameArena>): Promise<GameArena | undefined> {
    const result = await db.update(gameArenas)
      .set(updates)
      .where(eq(gameArenas.id, id))
      .returning();

    return result[0];
  }

  async deleteGameArena(id: number): Promise<boolean> {
    const result = await db.delete(gameArenas)
      .where(eq(gameArenas.id, id))
      .returning();

    return result.length > 0;
  }

  // Game Player methods
  async addPlayerToArena(player: InsertGamePlayer): Promise<GamePlayer> {
    const now = new Date();

    // Generate random spawn position
    const spawnX = (Math.random() - 0.5) * 50;
    const spawnY = 5; // Fixed height for spawning
    const spawnZ = (Math.random() - 0.5) * 50;

    const gamePlayer = {
      arenaId: player.arenaId,
      playerId: player.playerId,
      playerName: player.playerName,
      teamId: player.teamId || null,
      isHost: player.isHost || false,
      score: 0,
      spectersCaptured: 0,
      joinedAt: now,
      lastActive: now,
      status: 'active',
      positionX: spawnX,
      positionY: spawnY,
      positionZ: spawnZ
    };

    const result = await db.insert(gamePlayers)
      .values(gamePlayer)
      .returning();

    return result[0];
  }

  async getPlayersInArena(arenaId: number): Promise<GamePlayer[]> {
    return await db.select()
      .from(gamePlayers)
      .where(eq(gamePlayers.arenaId, arenaId));
  }

  async getActivePlayersInArena(arenaId: number): Promise<GamePlayer[]> {
    return await db.select()
      .from(gamePlayers)
      .where(and(
        eq(gamePlayers.arenaId, arenaId),
        eq(gamePlayers.status, 'active')
      ));
  }

  async getPlayerById(id: number): Promise<GamePlayer | undefined> {
    const result = await db.select()
      .from(gamePlayers)
      .where(eq(gamePlayers.id, id));

    return result[0];
  }

  async getPlayerBySessionId(sessionId: string): Promise<GamePlayer | undefined> {
    const result = await db.select()
      .from(gamePlayers)
      .where(eq(gamePlayers.playerId, sessionId));

    return result[0];
  }

  async updatePlayer(id: number, updates: Partial<GamePlayer>): Promise<GamePlayer | undefined> {
    // Update last active timestamp automatically
    updates.lastActive = new Date();

    const result = await db.update(gamePlayers)
      .set(updates)
      .where(eq(gamePlayers.id, id))
      .returning();

    return result[0];
  }

  async removePlayerFromArena(id: number): Promise<boolean> {
    const result = await db.delete(gamePlayers)
      .where(eq(gamePlayers.id, id))
      .returning();

    return result.length > 0;
  }

  // Game Team methods
  async createTeam(team: InsertGameTeam): Promise<GameTeam> {
    const gameTeam = {
      arenaId: team.arenaId,
      name: team.name,
      color: team.color,
      score: 0,
      spectersCaptured: 0
    };

    const result = await db.insert(gameTeams)
      .values(gameTeam)
      .returning();

    return result[0];
  }

  async getTeamById(id: number): Promise<GameTeam | undefined> {
    const result = await db.select()
      .from(gameTeams)
      .where(eq(gameTeams.id, id));

    return result[0];
  }

  async getTeamsInArena(arenaId: number): Promise<GameTeam[]> {
    return await db.select()
      .from(gameTeams)
      .where(eq(gameTeams.arenaId, arenaId));
  }

  async updateTeam(id: number, updates: Partial<GameTeam>): Promise<GameTeam | undefined> {
    const result = await db.update(gameTeams)
      .set(updates)
      .where(eq(gameTeams.id, id))
      .returning();

    return result[0];
  }

  async deleteTeam(id: number): Promise<boolean> {
    const result = await db.delete(gameTeams)
      .where(eq(gameTeams.id, id))
      .returning();

    return result.length > 0;
  }

  // Add a new method to get players in a team
  async getPlayersInTeam(teamId: number): Promise<GamePlayer[]> {
    return await db.select()
      .from(gamePlayers)
      .where(and(
        eq(gamePlayers.teamId, teamId),
        eq(gamePlayers.status, 'active')
      ));
  }

  // Level progression methods
  async incrementEnemiesDefeated(arenaId: number, amount: number = 1): Promise<number> {
    // Get the current arena
    const arenaResult = await db.select()
      .from(gameArenas)
      .where(eq(gameArenas.id, arenaId));

    const arena = arenaResult[0];
    if (!arena) return 0;

    // Calculate new value
    const newEnemiesDefeated = arena.enemiesDefeated + amount;

    // Update the arena
    await db.update(gameArenas)
      .set({ enemiesDefeated: newEnemiesDefeated })
      .where(eq(gameArenas.id, arenaId));

    return newEnemiesDefeated;
  }

  async advanceLevel(arenaId: number): Promise<GameArena | undefined> {
    // Get the current arena
    const arenaResult = await db.select()
      .from(gameArenas)
      .where(eq(gameArenas.id, arenaId));

    const arena = arenaResult[0];
    if (!arena) return undefined;

    // Calculate new level values
    const newLevel = arena.currentLevel + 1;

    // Calculate base enemies for first level
    const baseEnemies = 10;

    // Calculate enemies for current level (10% increase per level)
    const enemiesForLevel = Math.floor(baseEnemies * Math.pow(1.1, newLevel - 1));

    // Add boss every 10 levels
    const hasBoss = newLevel % 10 === 0;

    const newEnemiesTotal = Math.round(enemiesForLevel + (hasBoss ? 1 : 0));

    // Update the arena
    const result = await db.update(gameArenas)
      .set({
        currentLevel: newLevel,
        enemiesTotal: newEnemiesTotal,
        enemiesDefeated: 0
      })
      .where(eq(gameArenas.id, arenaId))
      .returning();

    return result[0];
  }
}
