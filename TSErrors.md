npx tsc --noEmit --project .
client/src/components/ui/toggle-group.tsx:20:4 - error TS2322: Type '{ children: Element; slot?: string | undefined; style?: CSSProperties | undefined; title?: string | undefined; key?: Key | null | undefined; defaultChecked?: boolean | undefined; ... 267 more ...; className: string; } | { ...; }' is not assignable to type 'IntrinsicAttributes & ((ToggleGroupSingleProps | ToggleGroupMultipleProps) & RefAttributes<HTMLDivElement>)'.
  Type '{ children: Element; slot?: string | undefined; style?: CSSProperties | undefined; title?: string | undefined; key?: Key | null | undefined; defaultChecked?: boolean | undefined; ... 267 more ...; className: string; }' is not assignable to type 'IntrinsicAttributes & ((ToggleGroupSingleProps | ToggleGroupMultipleProps) & RefAttributes<HTMLDivElement>)'.
    Type '{ children: Element; slot?: string | undefined; style?: CSSProperties | undefined; title?: string | undefined; key?: Key | null | undefined; defaultChecked?: boolean | undefined; ... 267 more ...; className: string; }' is not assignable to type 'ToggleGroupMultipleProps'.
      Types of property 'type' are incompatible.
        Type '"multiple" | "single"' is not assignable to type '"multiple"'.
          Type '"single"' is not assignable to type '"multiple"'.

20   <ToggleGroupPrimitive.Root
      ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/battle/TestTournamentBattle.ts:380:11 - error TS2322: Type 'number' is not assignable to type 'string'.

380           targetId: defenderId,
              ~~~~~~~~

  shared/schema.ts:286:3
    286   targetId?: string;
          ~~~~~~~~
    The expected type comes from property 'targetId' which is declared here on type 'RenderInstruction'

client/src/game/battle/TestTournamentBattle.ts:389:11 - error TS2322: Type 'number' is not assignable to type 'string'.

389           targetId: defenderId,
              ~~~~~~~~

  shared/schema.ts:286:3
    286   targetId?: string;
          ~~~~~~~~
    The expected type comes from property 'targetId' which is declared here on type 'RenderInstruction'

client/src/game/battle/TournamentBattleRenderer.ts:91:7 - error TS2345: Argument of type 'string' is not assignable to parameter of type 'Scene'.

91       String(petData.id),
         ~~~~~~~~~~~~~~~~~~


Found 4 errors in 3 files.

Errors  Files
     1  client/src/components/ui/toggle-group.tsx:20
     2  client/src/game/battle/TestTournamentBattle.ts:380
     1  client/src/game/battle/TournamentBattleRenderer.ts: