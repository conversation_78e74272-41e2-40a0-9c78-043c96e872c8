#!/bin/bash
set -e

# ShatterShift Deployment Script for VPS
echo "Starting SpecterShift deployment..."

# Define variables
DEPLOY_DIR="/opt/spectershift"
DOMAIN="spectershift.merchgenieai.com"
NODE_VERSION="18"

# Create deployment directory
echo "Creating deployment directory at $DEPLOY_DIR..."
mkdir -p $DEPLOY_DIR
cd $DEPLOY_DIR

# Check if this is an update or a fresh install
IS_UPDATE=false
if pm2 list | grep -q "shattershift"; then
  IS_UPDATE=true
  echo "Detected existing installation. Running in update mode."
fi

# Install Node.js if not already installed
if ! command -v node &> /dev/null || [ "$(node -v | cut -d. -f1 | tr -d 'v')" -lt "$NODE_VERSION" ]; then
  echo "Installing Node.js $NODE_VERSION..."
  curl -fsSL https://deb.nodesource.com/setup_$NODE_VERSION.x | bash -
  apt-get install -y nodejs
fi

# Install PM2 if not already installed
if ! command -v pm2 &> /dev/null; then
  echo "Installing PM2..."
  npm install -g pm2
fi

echo "Installing dependencies..."
# Install yarn if not already installed
if ! command -v yarn &> /dev/null; then
  echo "Installing yarn globally..."
  npm install -g yarn
fi
# Use yarn instead of npm ci
yarn install

# Clean the old build directory to ensure a fresh build
echo "Cleaning old build directory: $DEPLOY_DIR/dist..."
rm -rf "$DEPLOY_DIR/dist"
mkdir -p "$DEPLOY_DIR/dist"

# Build the application
echo "Building application..."
npm run build

# Create necessary directories for uploads if they don't exist
echo "Setting up upload directories..."
mkdir -p "$DEPLOY_DIR/uploads/images"
mkdir -p "$DEPLOY_DIR/uploads/nft-images"
mkdir -p "$DEPLOY_DIR/uploads/nft-metadata"

# Copy .env file if it exists in the project root
if [ -f "$DEPLOY_DIR/.env" ]; then
  echo "Using existing .env file..."
else
  echo "Creating new .env file..."
  cp "$DEPLOY_DIR/.env.example" "$DEPLOY_DIR/.env"
  # You may want to set specific environment variables here
  # echo "CUSTOM_VAR=value" >> "$DEPLOY_DIR/.env"
fi

# Run database migrations before starting the server
echo "Running database migrations..."
if [ -f "dist/migrate.js" ]; then
  # Load environment variables and run migrations
  NODE_ENV=production node dist/migrate.js || echo "Warning: Migration failed, but continuing deployment..."
else
  echo "Warning: migrate.js not found, skipping migrations"
fi

# Update the host in server/index.ts to listen on 0.0.0.0 instead of localhost
# This change should ideally be handled by environment variables, not sed, but keeping for now.
if [ -f "dist/index.js" ]; then
  echo "Updating host in dist/index.js to 0.0.0.0 for production..."
  sed -i 's/host: "localhost"/host: "0.0.0.0"/g' dist/index.js
  sed -i 's/host: "127.0.0.1"/host: "0.0.0.0"/g' dist/index.js
else
  echo "Warning: dist/index.js not found for host update. Skipping host modification."
fi

# Only create PM2 configuration if it doesn't exist or if this is a fresh install
if [ "$IS_UPDATE" = false ] || [ ! -f "ecosystem.config.js" ]; then
  echo "Creating PM2 configuration..."
  cat > ecosystem.config.js << EOL
module.exports = {
  apps: [{
    name: "shattershift",
    script: "dist/index.js",
    env_file: ".env",
    env: {
      NODE_ENV: "production",
      PORT: 5001
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: "1G"
  }]
};
EOL
fi

# Start or restart the application
if [ "$IS_UPDATE" = true ]; then
  echo "Restarting application with PM2..."
  pm2 restart shattershift
else
  echo "Starting application with PM2..."
  pm2 start ecosystem.config.js
fi

# Save PM2 process list to restart on reboot
pm2 save

# Only set up Nginx if this is a fresh install
if [ "$IS_UPDATE" = false ]; then
  # Set up Nginx configuration for the subdomain
  echo "Setting up Nginx configuration..."
  cat > /etc/nginx/sites-available/$DOMAIN << EOL
server {
    listen 80;
    server_name $DOMAIN;

    location / {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }

    location /ws {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 86400;
    }
}
EOL

  # Enable the site
  ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/

  # Check nginx configuration
  nginx -t

  # Reload Nginx configuration
  systemctl reload nginx

  # Set up SSL with Let's Encrypt (requires certbot)
  if command -v certbot &> /dev/null; then
    echo "Setting up SSL with Let's Encrypt..."
    certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>
  else
    echo "Certbot not found. Skipping SSL setup."
    echo "To install Certbot and set up SSL:"
    echo "apt-get install -y certbot python3-certbot-nginx"
    echo "Then run: certbot --nginx -d $DOMAIN"
  fi
else
  echo "Skipping Nginx setup as this is an update."
fi

echo "Deployment complete! SpecterShift should be available at https://$DOMAIN"
echo "Note: If you encounter any issues, check the logs with: pm2 logs shattershift"